# Design Document

## Overview

自动网页打开器是一个基于Python的桌面应用程序，使用PyQt5作为GUI框架。软件采用事件驱动架构，支持多线程操作以确保界面响应性。程序将读取包含URL的文本文件，按照用户设定的时间间隔依次在默认浏览器中打开这些网址。

## Architecture

### 系统架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   GUI Layer     │    │  Control Layer  │    │  Service Layer  │
│                 │    │                 │    │                 │
│ - Main Window   │◄──►│ - App Controller│◄──►│ - URL Manager   │
│ - File Dialog   │    │ - State Manager │    │ - Browser Opener│
│ - Status Display│    │ - Timer Control │    │ - File Reader   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 主要组件
1. **GUI Layer**: 负责用户界面展示和用户交互
2. **Control Layer**: 处理业务逻辑和状态管理
3. **Service Layer**: 提供核心功能服务

## Components and Interfaces

### 1. MainWindow (GUI组件)
```python
from PyQt5.QtWidgets import QMainWindow, QWidget, QVBoxLayout, QHBoxLayout
from PyQt5.QtWidgets import QPushButton, QLabel, QSpinBox, QProgressBar, QTextEdit
from PyQt5.QtCore import QTimer, pyqtSignal

class MainWindow(QMainWindow):
    def __init__(self)
    def setup_ui(self)
    def on_file_select(self)
    def on_start_click(self)
    def on_pause_click(self)
    def on_stop_click(self)
    def update_status(self, status: str)
    def update_progress(self, current: int, total: int)
    def closeEvent(self, event)  # 处理窗口关闭事件
```

**职责:**
- 创建和管理主窗口界面
- 处理用户交互事件
- 显示状态和进度信息
- 管理控件的启用/禁用状态
- 处理窗口生命周期事件

### 2. URLManager (服务组件)
```python
class URLManager:
    def __init__(self)
    def load_urls_from_file(self, file_path: str) -> List[str]
    def validate_url(self, url: str) -> bool
    def get_next_url(self) -> Optional[str]
    def reset_position(self)
    def get_progress(self) -> Tuple[int, int]
```

**职责:**
- 从文件读取URL列表
- 验证URL格式
- 管理当前处理位置
- 提供进度信息

### 3. BrowserOpener (服务组件)
```python
class BrowserOpener:
    @staticmethod
    def open_url(url: str) -> bool
    @staticmethod
    def is_valid_url(url: str) -> bool
```

**职责:**
- 调用系统默认浏览器打开URL
- 处理浏览器启动异常

### 4. AppController (控制组件)
```python
from PyQt5.QtCore import QObject, QTimer, QThread, pyqtSignal

class URLWorker(QThread):
    progress_updated = pyqtSignal(int, int)
    status_updated = pyqtSignal(str)
    error_occurred = pyqtSignal(str)
    
    def run(self)

class AppController(QObject):
    def __init__(self, main_window: MainWindow)
    def load_file(self, file_path: str)
    def start_process(self)
    def pause_process(self)
    def stop_process(self)
    def set_interval(self, seconds: float)
```

**职责:**
- 协调各组件工作
- 管理应用程序状态
- 控制URL处理流程
- 管理QTimer和QThread

## Data Models

### 1. ApplicationState (枚举)
```python
from enum import Enum

class ApplicationState(Enum):
    IDLE = "空闲"
    RUNNING = "运行中"
    PAUSED = "已暂停"
    STOPPED = "已停止"
```

### 2. URLData (数据类)
```python
@dataclass
class URLData:
    urls: List[str]
    current_index: int = 0
    total_count: int = 0
    
    def __post_init__(self):
        self.total_count = len(self.urls)
```

### 3. AppConfig (配置类)
```python
@dataclass
class AppConfig:
    default_interval: float = 5.0
    window_title: str = "自动网页打开器"
    window_size: Tuple[int, int] = (500, 300)
    supported_extensions: List[str] = [".txt"]
```

## Error Handling

### 错误类型和处理策略

1. **文件读取错误**
   - FileNotFoundError: 显示"文件不存在"错误信息
   - PermissionError: 显示"文件访问权限不足"错误信息
   - UnicodeDecodeError: 尝试不同编码格式读取

2. **URL格式错误**
   - 跳过无效URL并记录到日志
   - 在状态栏显示跳过的URL数量

3. **浏览器启动错误**
   - 捕获webbrowser模块异常
   - 显示"无法启动浏览器"错误信息

4. **线程异常**
   - 使用try-catch包装线程执行代码
   - 异常时自动停止处理并恢复到空闲状态

### 错误处理实现
```python
from PyQt5.QtWidgets import QMessageBox

def safe_execute(func, error_message: str):
    try:
        return func()
    except Exception as e:
        logging.error(f"{error_message}: {str(e)}")
        QMessageBox.critical(None, "错误", f"{error_message}: {str(e)}")
        return None
```

## Testing Strategy

### 1. 单元测试
- **URLManager测试**: 测试URL加载、验证、位置管理功能
- **BrowserOpener测试**: 测试URL打开功能（使用mock）
- **AppController测试**: 测试状态管理和流程控制

### 2. 集成测试
- **文件加载流程测试**: 测试从文件选择到URL加载的完整流程
- **运行控制测试**: 测试开始、暂停、停止的状态转换
- **错误处理测试**: 测试各种异常情况的处理

### 3. 用户界面测试
- **控件状态测试**: 验证不同状态下控件的启用/禁用
- **进度显示测试**: 验证进度信息的正确显示
- **用户交互测试**: 测试按钮点击和文件选择功能

### 测试工具
- unittest: Python标准测试框架
- unittest.mock: 模拟外部依赖
- pytest-qt: PyQt5应用程序测试支持

## Implementation Notes

### 技术选择说明

1. **GUI框架**: 选择PyQt5
   - 原因: 功能丰富，界面美观，信号槽机制强大
   - 优势: 专业级GUI框架，更好的用户体验
   - 安装: pip install PyQt5

2. **多线程**: 使用QThread
   - 原因: 避免GUI界面冻结，与PyQt5集成更好
   - 实现: QThread处理URL，信号槽更新界面

3. **浏览器调用**: 使用webbrowser模块
   - 原因: Python标准库，自动调用系统默认浏览器
   - 优势: 跨平台兼容，无需配置

4. **文件处理**: 支持多种编码格式
   - 默认UTF-8，失败时尝试GBK和GB2312
   - 处理中文文件名和内容

### 性能考虑

1. **内存使用**: 
   - 一次性加载所有URL到内存
   - 对于大文件（>10000行）考虑分批加载

2. **响应性**: 
   - GUI操作在主线程
   - URL处理在QThread后台线程
   - 使用PyQt5信号槽进行线程间通信

3. **资源管理**:
   - 及时关闭文件句柄
   - 线程结束时清理资源