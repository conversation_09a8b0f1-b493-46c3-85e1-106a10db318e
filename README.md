# 自动网页打开器

一个用于按时间间隔自动打开网址列表的桌面应用程序。

## 功能特点

- 📁 **文件加载**: 支持从文本文件加载URL列表
- ⏱️ **时间控制**: 可自定义时间间隔（0.5秒到1小时）
- 🎮 **流程控制**: 支持开始、暂停、停止操作
- 📊 **进度显示**: 实时显示处理进度和当前URL
- 🛡️ **错误处理**: 完善的错误处理和日志记录
- 🎨 **现代界面**: 基于PyQt5的现代化图形界面
- ⌨️ **快捷键**: 支持键盘快捷键操作

## 系统要求

- Python 3.8 或更高版本
- PyQt5 5.15.0 或更高版本
- Windows / macOS / Linux

## 安装方法

### 方法一：使用pip安装（推荐）

```bash
# 克隆项目
git clone <repository-url>
cd auto-webpage-opener

# 安装依赖
pip install -r requirements.txt

# 运行程序
python main.py
```

### 方法二：使用setup.py安装

```bash
# 安装到系统
python setup.py install

# 运行程序
auto-webpage-opener
```

### 方法三：直接运行可执行文件

下载发布版本中的可执行文件，双击运行即可。

## 使用方法

1. **启动程序**: 运行 `python main.py` 或双击可执行文件
2. **选择文件**: 点击"选择URL文件"按钮，选择包含网址的文本文件
3. **设置间隔**: 调整时间间隔设置（默认5秒）
4. **开始运行**: 点击"开始"按钮开始自动打开网址
5. **控制流程**: 使用暂停/停止按钮控制执行流程

## URL文件格式

创建一个文本文件，每行包含一个网址：

```
# 这是注释行，会被忽略
http://www.example.com
https://www.google.com
https://www.github.com

# 支持的协议：http:// 和 https://
# 空行会被自动忽略
```

## 快捷键

- `Ctrl+O`: 打开文件
- `F5`: 开始处理
- `F6`: 暂停处理
- `F7` / `Esc`: 停止处理
- `Ctrl+Q`: 退出程序

## 项目结构

```
auto-webpage-opener/
├── auto_webpage_opener/          # 主程序包
│   ├── gui/                      # 图形界面模块
│   ├── services/                 # 业务服务模块
│   ├── controllers/              # 控制器模块
│   ├── models/                   # 数据模型模块
│   └── utils/                    # 工具模块
├── env_test/                     # 测试文件
├── package/                      # 打包相关文件
├── main.py                       # 主程序入口
├── requirements.txt              # 依赖列表
├── setup.py                      # 安装脚本
└── README.md                     # 说明文档
```

## 开发和测试

### 运行测试

```bash
# 运行所有测试
cd env_test
python run_all_tests.py

# 运行单个测试
python test_url_manager.py
python test_browser_opener.py
python test_integration.py
```

### 打包为可执行文件

```bash
# 安装PyInstaller
pip install pyinstaller

# 运行打包脚本
cd package
python build_exe.py
```

## 配置选项

程序支持以下配置选项（在 `AppConfig` 类中定义）：

- `default_interval`: 默认时间间隔（秒）
- `min_interval`: 最小时间间隔（秒）
- `max_interval`: 最大时间间隔（秒）
- `window_size`: 窗口大小
- `supported_extensions`: 支持的文件扩展名
- `log_level`: 日志级别

## 日志文件

程序运行时会生成日志文件 `auto_webpage_opener.log`，记录：

- 程序启动和关闭
- 文件加载过程
- URL处理状态
- 错误和警告信息

## 故障排除

### 常见问题

1. **程序无法启动**
   - 检查Python版本是否为3.8+
   - 确认PyQt5已正确安装

2. **无法打开网址**
   - 检查网址格式是否正确
   - 确认系统默认浏览器设置正常

3. **文件加载失败**
   - 检查文件编码（支持UTF-8、GBK、GB2312）
   - 确认文件中包含有效的URL

### 获取帮助

如果遇到问题，请：

1. 查看日志文件 `auto_webpage_opener.log`
2. 运行测试套件检查环境
3. 提交Issue并附上错误信息

## 许可证

本项目采用 MIT 许可证。详见 LICENSE 文件。

## 贡献

欢迎提交Pull Request和Issue！

## 更新日志

### v1.0.0 (2025-07-29)

- 初始版本发布
- 实现基本的URL自动打开功能
- 支持文件加载和时间间隔设置
- 添加图形界面和快捷键支持
- 完善错误处理和日志记录
- 提供完整的测试套件
