"""
应用程序主类
整合所有组件，提供完整的应用程序功能
"""

import sys
import logging
from typing import Optional
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

from .gui.main_window import MainWindow
from .controllers.app_controller import AppController
from .models.app_config import AppConfig
from .utils.error_handler import setup_logging, ErrorHandler


class AutoWebpageOpenerApp:
    """自动网页打开器应用程序类"""
    
    def __init__(self, config: Optional[AppConfig] = None):
        """
        初始化应用程序
        
        Args:
            config: 应用程序配置
        """
        self.config = config or AppConfig.get_default_config()
        
        # 设置日志
        self.logger = setup_logging(
            self.config.log_file,
            self.config.log_level,
            self.config.log_format
        )
        
        self.error_handler = ErrorHandler(self.logger)
        
        # 初始化Qt应用程序
        self.qt_app = QApplication.instance()
        if self.qt_app is None:
            self.qt_app = QApplication(sys.argv)
        
        # 设置应用程序属性
        self.qt_app.setApplicationName(self.config.window_title)
        self.qt_app.setApplicationVersion("1.0.0")

        # 设置退出行为：不要在最后一个窗口关闭时自动退出
        self.qt_app.setQuitOnLastWindowClosed(False)
        
        # 初始化组件
        self.main_window: Optional[MainWindow] = None
        self.app_controller: Optional[AppController] = None
        
        self.logger.info("自动网页打开器应用程序初始化完成")
    
    def initialize_components(self):
        """初始化应用程序组件"""
        try:
            # 创建主窗口
            self.main_window = MainWindow(self.config)
            
            # 创建应用程序控制器
            self.app_controller = AppController(self.main_window, self.config)
            
            # 连接应用程序级别的信号
            self.connect_app_signals()
            
            self.logger.info("应用程序组件初始化完成")
            
        except Exception as e:
            error_msg = f"初始化组件时发生错误: {str(e)}"
            self.logger.error(error_msg)
            raise RuntimeError(error_msg)
    
    def connect_app_signals(self):
        """连接应用程序级别的信号"""
        # 连接Qt应用程序的退出信号
        self.qt_app.aboutToQuit.connect(self.cleanup)
        
        # 设置异常处理
        sys.excepthook = self.handle_exception
    
    def handle_exception(self, exc_type, exc_value, exc_traceback):
        """
        全局异常处理器
        
        Args:
            exc_type: 异常类型
            exc_value: 异常值
            exc_traceback: 异常追踪
        """
        if issubclass(exc_type, KeyboardInterrupt):
            # 处理Ctrl+C中断
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
        
        error_msg = f"未处理的异常: {exc_type.__name__}: {exc_value}"
        self.logger.error(error_msg, exc_info=(exc_type, exc_value, exc_traceback))
        
        # 如果主窗口存在，显示错误对话框
        if self.main_window:
            self.main_window.show_error_message(
                "程序错误",
                f"程序发生未预期的错误：\n{error_msg}\n\n请查看日志文件获取详细信息。"
            )
    
    def run(self) -> int:
        """
        运行应用程序
        
        Returns:
            int: 应用程序退出代码
        """
        try:
            # 初始化组件
            self.initialize_components()
            
            # 显示主窗口
            if self.main_window:
                self.main_window.show()
                self.logger.info("主窗口已显示")
            
            # 运行Qt事件循环
            self.logger.info("应用程序开始运行")
            exit_code = self.qt_app.exec_()
            
            self.logger.info(f"应用程序退出，退出代码: {exit_code}")
            return exit_code
            
        except Exception as e:
            error_msg = f"运行应用程序时发生错误: {str(e)}"
            self.logger.error(error_msg)
            print(f"ERROR: {error_msg}")
            return 1
    
    def cleanup(self):
        """清理应用程序资源"""
        try:
            self.logger.info("开始清理应用程序资源")
            
            # 清理控制器
            if self.app_controller:
                self.app_controller.cleanup()
            
            # 清理主窗口
            if self.main_window:
                self.main_window.close()
            
            self.logger.info("应用程序资源清理完成")
            
        except Exception as e:
            self.logger.error(f"清理资源时发生错误: {str(e)}")
    
    def get_main_window(self) -> Optional[MainWindow]:
        """
        获取主窗口实例
        
        Returns:
            Optional[MainWindow]: 主窗口实例
        """
        return self.main_window
    
    def get_app_controller(self) -> Optional[AppController]:
        """
        获取应用程序控制器实例
        
        Returns:
            Optional[AppController]: 应用程序控制器实例
        """
        return self.app_controller
    
    @staticmethod
    def create_and_run(config: Optional[AppConfig] = None) -> int:
        """
        创建并运行应用程序的便捷方法
        
        Args:
            config: 应用程序配置
            
        Returns:
            int: 应用程序退出代码
        """
        app = AutoWebpageOpenerApp(config)
        return app.run()


def main():
    """主函数入口"""
    try:
        # 创建默认配置
        config = AppConfig.get_default_config()
        
        # 创建并运行应用程序
        exit_code = AutoWebpageOpenerApp.create_and_run(config)
        
        sys.exit(exit_code)
        
    except Exception as e:
        print(f"FATAL ERROR: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
