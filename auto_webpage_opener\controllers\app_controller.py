"""
应用程序控制器
协调各组件工作，管理应用程序状态和流程
"""

import logging
from typing import Optional
from PyQt5.QtCore import QObject

from ..gui.main_window import MainWindow
from ..services.url_manager import URLManager
from ..services.browser_opener import BrowserOpener
from ..controllers.url_worker import URLWorker
from ..models.app_config import AppConfig
from ..models.app_state import ApplicationState
from ..utils.error_handler import ErrorHandler


class AppController(QObject):
    """应用程序控制器类"""
    
    def __init__(self, main_window: MainWindow, config: Optional[AppConfig] = None):
        """
        初始化应用程序控制器
        
        Args:
            main_window: 主窗口实例
            config: 应用程序配置
        """
        super().__init__()
        
        self.main_window = main_window
        self.config = config or AppConfig.get_default_config()
        self.logger = logging.getLogger("auto_webpage_opener.controllers.app_controller")
        self.error_handler = ErrorHandler(self.logger)
        
        # 初始化服务组件
        self.url_manager = URLManager(self.config)
        self.browser_opener = BrowserOpener()
        
        # 初始化工作线程
        self.url_worker: Optional[URLWorker] = None
        
        # 应用程序状态
        self.current_state = ApplicationState.IDLE
        
        # 连接信号
        self.connect_signals()
        
        # 初始化界面状态
        self.main_window.set_state(self.current_state)
        
        self.logger.info("应用程序控制器初始化完成")
    
    def connect_signals(self):
        """连接GUI信号到控制器方法"""
        self.main_window.file_selected.connect(self.load_file)
        self.main_window.start_clicked.connect(self.start_process)
        self.main_window.pause_clicked.connect(self.pause_process)
        self.main_window.stop_clicked.connect(self.stop_process)
        self.main_window.interval_changed.connect(self.set_interval)
    
    def load_file(self, file_path: str):
        """
        加载URL文件
        
        Args:
            file_path: 文件路径
        """
        try:
            self.logger.info(f"开始加载文件: {file_path}")
            
            # 加载URL文件
            success = self.url_manager.load_urls_from_file(file_path)
            
            if success:
                # 更新进度显示
                current, total = self.url_manager.get_progress()
                self.main_window.update_progress(current, total)
                
                self.logger.info(f"文件加载成功，共 {total} 个URL")
                self.main_window.show_info_message(
                    "文件加载成功", 
                    f"成功加载 {total} 个有效URL"
                )
            else:
                self.logger.error(f"文件加载失败: {file_path}")
                self.main_window.show_error_message(
                    "文件加载失败", 
                    "无法加载文件或文件中没有有效的URL"
                )
                
        except Exception as e:
            error_msg = self.error_handler.handle_file_error(e, file_path)
            self.main_window.show_error_message("文件加载错误", error_msg)
    
    def start_process(self):
        """开始URL处理流程"""
        try:
            if self.current_state == ApplicationState.PAUSED:
                # 从暂停状态恢复
                self.resume_process()
                return
            
            # 检查是否有加载的URL
            if self.url_manager.get_url_count() == 0:
                self.main_window.show_error_message(
                    "无法开始", 
                    "请先选择并加载URL文件"
                )
                return
            
            # 测试浏览器可用性
            if not self.browser_opener.test_browser_availability():
                self.main_window.show_error_message(
                    "浏览器错误", 
                    "无法启动默认浏览器，请检查系统设置"
                )
                return
            
            # 创建并启动工作线程
            self.url_worker = URLWorker(self.url_manager, self.browser_opener)
            self.connect_worker_signals()
            
            # 设置当前时间间隔
            interval = self.main_window.get_interval()
            self.url_worker.set_interval(interval)
            
            # 启动线程
            self.url_worker.start()
            
            # 更新状态
            self.set_state(ApplicationState.RUNNING)
            
            self.logger.info("URL处理流程已开始")
            
        except Exception as e:
            error_msg = f"启动处理流程时发生错误: {str(e)}"
            self.logger.error(error_msg)
            self.main_window.show_error_message("启动错误", error_msg)
    
    def pause_process(self):
        """暂停URL处理流程"""
        try:
            if self.url_worker and self.url_worker.is_running():
                self.url_worker.pause_work()
                self.set_state(ApplicationState.PAUSED)
                self.logger.info("URL处理流程已暂停")
            
        except Exception as e:
            error_msg = f"暂停处理流程时发生错误: {str(e)}"
            self.logger.error(error_msg)
            self.main_window.show_error_message("暂停错误", error_msg)
    
    def resume_process(self):
        """恢复URL处理流程"""
        try:
            if self.url_worker and self.url_worker.is_paused():
                self.url_worker.resume_work()
                self.set_state(ApplicationState.RUNNING)
                self.logger.info("URL处理流程已恢复")
            
        except Exception as e:
            error_msg = f"恢复处理流程时发生错误: {str(e)}"
            self.logger.error(error_msg)
            self.main_window.show_error_message("恢复错误", error_msg)
    
    def stop_process(self):
        """停止URL处理流程"""
        try:
            if self.url_worker:
                self.url_worker.stop_work()
                self.url_worker.wait()  # 等待线程结束
                self.url_worker = None
            
            # 重置URL位置
            self.url_manager.reset_position()
            
            # 更新界面
            self.main_window.update_current_url("")
            current, total = self.url_manager.get_progress()
            self.main_window.update_progress(current, total)
            
            # 更新状态
            self.set_state(ApplicationState.STOPPED)
            
            self.logger.info("URL处理流程已停止")
            
        except Exception as e:
            error_msg = f"停止处理流程时发生错误: {str(e)}"
            self.logger.error(error_msg)
            self.main_window.show_error_message("停止错误", error_msg)
    
    def set_interval(self, interval: float):
        """
        设置时间间隔
        
        Args:
            interval: 时间间隔（秒）
        """
        try:
            # 验证时间间隔
            if not self.config.validate_interval(interval):
                self.logger.warning(f"无效的时间间隔: {interval}")
                return
            
            # 如果工作线程正在运行，更新其时间间隔
            if self.url_worker and self.url_worker.is_running():
                self.url_worker.set_interval(interval)
            
            self.logger.info(f"时间间隔已设置为: {interval}秒")
            
        except Exception as e:
            error_msg = f"设置时间间隔时发生错误: {str(e)}"
            self.logger.error(error_msg)
    
    def connect_worker_signals(self):
        """连接工作线程信号"""
        if self.url_worker:
            self.url_worker.progress_updated.connect(self.main_window.update_progress)
            self.url_worker.status_updated.connect(self.on_status_updated)
            self.url_worker.current_url_updated.connect(self.main_window.update_current_url)
            self.url_worker.error_occurred.connect(self.on_error_occurred)
            self.url_worker.finished_all.connect(self.on_finished_all)
    
    def on_status_updated(self, status: str):
        """
        处理状态更新
        
        Args:
            status: 状态字符串
        """
        self.main_window.update_status(status)
        
        # 更新内部状态
        for state in ApplicationState:
            if str(state) == status:
                self.current_state = state
                break
    
    def on_error_occurred(self, error_message: str):
        """
        处理错误发生
        
        Args:
            error_message: 错误消息
        """
        self.logger.warning(f"工作线程报告错误: {error_message}")
        # 可以选择显示错误消息或只记录日志
        # self.main_window.show_error_message("处理错误", error_message)
    
    def on_finished_all(self):
        """处理全部URL完成"""
        self.logger.info("所有URL处理完成")
        
        # 清理工作线程
        if self.url_worker:
            self.url_worker.wait()
            self.url_worker = None
        
        # 更新状态
        self.set_state(ApplicationState.IDLE)
        self.main_window.update_current_url("")
        
        # 显示完成消息
        self.main_window.show_info_message(
            "处理完成", 
            "所有URL已处理完成！"
        )
    
    def set_state(self, state: ApplicationState):
        """
        设置应用程序状态
        
        Args:
            state: 应用程序状态
        """
        self.current_state = state
        self.main_window.set_state(state)
        self.logger.info(f"应用程序状态更新为: {state}")
    
    def cleanup(self):
        """清理资源"""
        try:
            if self.url_worker and self.url_worker.isRunning():
                self.url_worker.stop_work()
                self.url_worker.wait(5000)  # 等待最多5秒
                
            self.logger.info("应用程序控制器资源清理完成")
            
        except Exception as e:
            self.logger.error(f"清理资源时发生错误: {str(e)}")
