"""
URL处理工作线程
在后台线程中处理URL打开任务，避免阻塞GUI界面
"""

import logging
import time
from typing import Optional
from PyQt5.QtCore import QThread, pyqtSignal, QMutex, QWaitCondition

from ..services.url_manager import URLManager
from ..services.browser_opener import BrowserOpener
from ..models.app_state import ApplicationState


class URLWorker(QThread):
    """URL处理工作线程"""
    
    # 定义信号
    progress_updated = pyqtSignal(int, int)  # 进度更新信号 (当前, 总数)
    status_updated = pyqtSignal(str)         # 状态更新信号
    current_url_updated = pyqtSignal(str)    # 当前URL更新信号
    error_occurred = pyqtSignal(str)         # 错误发生信号
    finished_all = pyqtSignal()              # 全部完成信号
    
    def __init__(self, url_manager: URLManager, browser_opener: BrowserOpener):
        """
        初始化URL工作线程
        
        Args:
            url_manager: URL管理器
            browser_opener: 浏览器打开器
        """
        super().__init__()
        
        self.url_manager = url_manager
        self.browser_opener = browser_opener
        self.logger = logging.getLogger("auto_webpage_opener.controllers.url_worker")
        
        # 线程控制
        self._running = False
        self._paused = False
        self._stopped = False
        self._interval = 5.0
        
        # 线程同步
        self._mutex = QMutex()
        self._pause_condition = QWaitCondition()
        
    def set_interval(self, interval: float):
        """
        设置时间间隔
        
        Args:
            interval: 时间间隔（秒）
        """
        self._mutex.lock()
        self._interval = interval
        self._mutex.unlock()
        self.logger.info(f"时间间隔设置为: {interval}秒")
    
    def pause_work(self):
        """暂停工作"""
        self._mutex.lock()
        self._paused = True
        self._mutex.unlock()
        self.status_updated.emit(str(ApplicationState.PAUSED))
        self.logger.info("工作线程已暂停")
    
    def resume_work(self):
        """恢复工作"""
        self._mutex.lock()
        self._paused = False
        self._pause_condition.wakeAll()
        self._mutex.unlock()
        self.status_updated.emit(str(ApplicationState.RUNNING))
        self.logger.info("工作线程已恢复")
    
    def stop_work(self):
        """停止工作"""
        self._mutex.lock()
        self._stopped = True
        self._running = False
        self._paused = False
        self._pause_condition.wakeAll()
        self._mutex.unlock()
        self.status_updated.emit(str(ApplicationState.STOPPED))
        self.logger.info("工作线程已停止")
    
    def run(self):
        """线程主运行方法"""
        try:
            self._running = True
            self._stopped = False
            self._paused = False
            
            self.status_updated.emit(str(ApplicationState.RUNNING))
            self.logger.info("URL处理线程开始运行")
            
            # 重置URL管理器位置（如果需要从头开始）
            if self.url_manager.is_finished():
                self.url_manager.reset_position()
            
            while self._running and not self._stopped:
                # 检查是否暂停
                self._mutex.lock()
                if self._paused:
                    self.logger.info("线程进入暂停状态")
                    self._pause_condition.wait(self._mutex)
                    self.logger.info("线程从暂停状态恢复")
                self._mutex.unlock()
                
                # 检查是否停止
                if self._stopped:
                    break
                
                # 获取下一个URL
                url = self.url_manager.get_next_url()
                if url is None:
                    # 所有URL处理完成
                    self.logger.info("所有URL处理完成")
                    self.finished_all.emit()
                    break
                
                # 更新当前URL显示
                self.current_url_updated.emit(url)
                
                # 更新进度
                current, total = self.url_manager.get_progress()
                self.progress_updated.emit(current, total)
                
                self.logger.info(f"正在处理URL ({current}/{total}): {url}")
                
                # 打开URL
                try:
                    success = self.browser_opener.open_url(url)
                    if not success:
                        error_msg = f"无法打开URL: {url}"
                        self.logger.warning(error_msg)
                        self.error_occurred.emit(error_msg)
                    else:
                        self.logger.info(f"成功打开URL: {url}")
                        
                except Exception as e:
                    error_msg = f"打开URL时发生异常: {url} - {str(e)}"
                    self.logger.error(error_msg)
                    self.error_occurred.emit(error_msg)
                
                # 等待指定的时间间隔
                if not self._stopped:
                    self._wait_with_interrupt(self._interval)
            
            # 线程结束
            self._running = False
            if not self._stopped:
                self.status_updated.emit(str(ApplicationState.IDLE))
            
            self.logger.info("URL处理线程结束")
            
        except Exception as e:
            error_msg = f"URL处理线程发生异常: {str(e)}"
            self.logger.error(error_msg)
            self.error_occurred.emit(error_msg)
            self.status_updated.emit(str(ApplicationState.STOPPED))
    
    def _wait_with_interrupt(self, seconds: float):
        """
        可中断的等待
        
        Args:
            seconds: 等待时间（秒）
        """
        # 将等待时间分成小段，以便能够响应暂停/停止信号
        wait_step = 0.1  # 每次等待0.1秒
        total_waited = 0.0
        
        while total_waited < seconds and self._running and not self._stopped:
            # 检查是否暂停
            self._mutex.lock()
            if self._paused:
                self._pause_condition.wait(self._mutex)
            self._mutex.unlock()
            
            if self._stopped:
                break
            
            # 等待一小段时间
            actual_wait = min(wait_step, seconds - total_waited)
            time.sleep(actual_wait)
            total_waited += actual_wait
    
    def is_running(self) -> bool:
        """
        检查线程是否正在运行
        
        Returns:
            bool: 是否正在运行
        """
        return self._running
    
    def is_paused(self) -> bool:
        """
        检查线程是否已暂停
        
        Returns:
            bool: 是否已暂停
        """
        return self._paused
    
    def is_stopped(self) -> bool:
        """
        检查线程是否已停止
        
        Returns:
            bool: 是否已停止
        """
        return self._stopped
