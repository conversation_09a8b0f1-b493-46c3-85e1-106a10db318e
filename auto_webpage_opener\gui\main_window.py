"""
主窗口界面
实现应用程序的主要用户界面
"""

import logging
from typing import Optional
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                             QPushButton, QLabel, QSpinBox, QProgressBar,
                             QTextEdit, QFileDialog, QMessageBox, QGroupBox,
                             QDoubleSpinBox, QFrame, QStatusBar, QMenuBar,
                             QAction, QShortcut, QSystemTrayIcon, QMenu)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QKeySequence, QIcon

from ..models.app_config import AppConfig
from ..models.app_state import ApplicationState


class MainWindow(QMainWindow):
    """主窗口类"""
    
    # 定义信号
    file_selected = pyqtSignal(str)  # 文件选择信号
    start_clicked = pyqtSignal()     # 开始按钮点击信号
    pause_clicked = pyqtSignal()     # 暂停按钮点击信号
    stop_clicked = pyqtSignal()      # 停止按钮点击信号
    interval_changed = pyqtSignal(float)  # 时间间隔改变信号
    timer_enabled_changed = pyqtSignal(bool)  # 定时器启用状态改变信号
    timer_time_changed = pyqtSignal(object)   # 定时时间改变信号
    
    def __init__(self, config: Optional[AppConfig] = None):
        """
        初始化主窗口
        
        Args:
            config: 应用程序配置
        """
        super().__init__()
        
        self.config = config or AppConfig.get_default_config()
        self.logger = logging.getLogger("auto_webpage_opener.gui.main_window")
        
        # 初始化状态
        self.current_state = ApplicationState.IDLE
        self.selected_file_path = ""
        
        # 设置窗口
        self.setup_window()

        # 创建界面
        self.setup_ui()

        # 设置样式
        self.set_window_style()

        # 设置快捷键
        self.setup_shortcuts()

        # 连接信号
        self.connect_signals()

        # 更新界面状态
        self.update_ui_state()

        # 设置定时检查器
        self.setup_timer_checker()

        # 设置系统托盘
        self.setup_system_tray()
    
    def setup_window(self):
        """设置窗口属性"""
        self.setWindowTitle(self.config.window_title)
        self.resize(*self.config.window_size)
        self.setMinimumSize(*self.config.window_min_size)

        # 设置窗口图标
        self.set_window_icon()

        # 设置窗口居中
        self.center_window()
    
    def center_window(self):
        """将窗口居中显示"""
        from PyQt5.QtWidgets import QDesktopWidget

        screen = QDesktopWidget().screenGeometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)

    def set_window_icon(self):
        """设置窗口图标"""
        import os
        from PyQt5.QtGui import QIcon, QPixmap

        # 尝试加载图标文件
        icon_paths = [
            os.path.join(os.path.dirname(__file__), "..", "..", "package", "app_icon.png"),
            os.path.join(os.path.dirname(__file__), "..", "..", "package", "app_icon.ico"),
            "app_icon.png",
            "app_icon.ico"
        ]

        icon_set = False
        for icon_path in icon_paths:
            if os.path.exists(icon_path):
                try:
                    icon = QIcon(icon_path)
                    if not icon.isNull():
                        self.setWindowIcon(icon)
                        icon_set = True
                        self.logger.info(f"成功设置窗口图标: {icon_path}")
                        break
                except Exception as e:
                    self.logger.warning(f"加载图标失败 {icon_path}: {e}")

        if not icon_set:
            # 如果没有找到图标文件，创建一个简单的默认图标
            self.create_default_icon()

    def create_default_icon(self):
        """创建地球样式的默认图标"""
        try:
            from PyQt5.QtGui import QIcon, QPixmap, QPainter, QBrush, QPen, QColor, QPolygon
            from PyQt5.QtCore import Qt, QPoint

            # 创建32x32的像素图
            pixmap = QPixmap(32, 32)
            pixmap.fill(Qt.transparent)

            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)

            # 绘制地球背景（蓝色）
            painter.setBrush(QBrush(QColor(30, 144, 255)))
            painter.setPen(QPen(QColor(0, 100, 200), 1))
            painter.drawEllipse(2, 2, 28, 28)

            # 绘制大陆（绿色）
            painter.setBrush(QBrush(QColor(34, 139, 34)))
            painter.setPen(Qt.NoPen)

            # 简化的大陆形状
            continent1 = QPolygon([
                QPoint(8, 10), QPoint(12, 8), QPoint(16, 10),
                QPoint(18, 14), QPoint(16, 18), QPoint(12, 20),
                QPoint(8, 18), QPoint(6, 14)
            ])
            painter.drawPolygon(continent1)

            continent2 = QPolygon([
                QPoint(20, 12), QPoint(24, 10), QPoint(26, 14),
                QPoint(24, 16), QPoint(20, 16)
            ])
            painter.drawPolygon(continent2)

            # 绘制经纬线（白色，半透明）
            painter.setPen(QPen(QColor(255, 255, 255, 100), 1))
            # 经线
            painter.drawArc(6, 2, 20, 28, 0, 180*16)  # 上半圆
            painter.drawArc(10, 2, 12, 28, 0, 180*16)  # 中间线
            painter.drawArc(14, 2, 8, 28, 0, 180*16)   # 右侧线

            # 纬线
            painter.drawLine(6, 10, 26, 10)  # 上纬线
            painter.drawLine(4, 16, 28, 16)  # 中纬线
            painter.drawLine(6, 22, 26, 22)  # 下纬线

            painter.end()

            # 设置图标
            icon = QIcon(pixmap)
            self.setWindowIcon(icon)
            self.logger.info("使用地球样式默认图标")

        except Exception as e:
            self.logger.warning(f"创建默认图标失败: {e}")

    def setup_timer_checker(self):
        """设置定时检查器"""
        # 创建定时检查器，每分钟检查一次
        self.timer_checker = QTimer()
        self.timer_checker.timeout.connect(self.check_scheduled_time)
        self.timer_checker.start(60000)  # 60秒检查一次

        # 记录上次检查的日期，用于每日重置
        from datetime import datetime
        self.last_check_date = datetime.now().date()
        self.timer_executed_today = False

    def setup_system_tray(self):
        """设置系统托盘"""
        # 检查系统是否支持托盘
        if not QSystemTrayIcon.isSystemTrayAvailable():
            self.logger.warning("系统不支持托盘功能")
            return

        # 创建托盘图标
        self.tray_icon = QSystemTrayIcon(self)

        # 设置托盘图标
        if hasattr(self, 'windowIcon') and not self.windowIcon().isNull():
            self.tray_icon.setIcon(self.windowIcon())
        else:
            # 如果没有窗口图标，创建默认图标
            self.create_default_icon()
            self.tray_icon.setIcon(self.windowIcon())

        # 创建托盘菜单
        tray_menu = QMenu()

        # 显示/隐藏窗口
        show_action = QAction("显示窗口", self)
        show_action.triggered.connect(self.show_window)
        tray_menu.addAction(show_action)

        hide_action = QAction("隐藏窗口", self)
        hide_action.triggered.connect(self.hide_window)
        tray_menu.addAction(hide_action)

        tray_menu.addSeparator()

        # 程序控制
        start_action = QAction("开始", self)
        start_action.triggered.connect(self.on_start_click)
        tray_menu.addAction(start_action)

        pause_action = QAction("暂停", self)
        pause_action.triggered.connect(self.on_pause_click)
        tray_menu.addAction(pause_action)

        stop_action = QAction("停止", self)
        stop_action.triggered.connect(self.on_stop_click)
        tray_menu.addAction(stop_action)

        tray_menu.addSeparator()

        # 退出程序
        quit_action = QAction("退出程序", self)
        quit_action.triggered.connect(self.quit_application)
        tray_menu.addAction(quit_action)

        # 设置托盘菜单
        self.tray_icon.setContextMenu(tray_menu)

        # 设置托盘提示
        self.tray_icon.setToolTip("自动网页打开器")

        # 连接托盘图标双击事件
        self.tray_icon.activated.connect(self.tray_icon_activated)

        # 显示托盘图标
        self.tray_icon.show()

        self.logger.info("系统托盘已设置")

    def tray_icon_activated(self, reason):
        """托盘图标激活事件"""
        if reason == QSystemTrayIcon.DoubleClick:
            # 双击托盘图标显示/隐藏窗口
            if self.isVisible():
                self.hide_window()
            else:
                self.show_window()

    def show_window(self):
        """显示窗口"""
        self.show()
        self.raise_()
        self.activateWindow()
        self.logger.info("窗口已显示")

    def hide_window(self):
        """隐藏窗口"""
        self.hide()
        self.logger.info("窗口已隐藏到托盘")

    def quit_application(self):
        """退出应用程序"""
        self.logger.info("用户选择退出程序")
        # 隐藏托盘图标
        if hasattr(self, 'tray_icon'):
            self.tray_icon.hide()
        # 退出应用程序
        QApplication.quit()

    def closeEvent(self, event):
        """重写关闭事件，点击关闭按钮时隐藏到托盘"""
        if hasattr(self, 'tray_icon') and self.tray_icon.isVisible():
            # 如果托盘可用，隐藏到托盘
            self.hide_window()

            # 首次隐藏时显示提示
            if not hasattr(self, '_tray_tip_shown'):
                self.tray_icon.showMessage(
                    "自动网页打开器",
                    "程序已最小化到系统托盘。双击托盘图标可重新显示窗口。",
                    QSystemTrayIcon.Information,
                    3000
                )
                self._tray_tip_shown = True

            event.ignore()  # 忽略关闭事件
        else:
            # 如果托盘不可用，正常关闭
            event.accept()

    def update_tray_tooltip(self, status_text: str = ""):
        """更新托盘图标提示信息"""
        if hasattr(self, 'tray_icon') and self.tray_icon.isVisible():
            base_text = "自动网页打开器"
            if status_text:
                tooltip = f"{base_text}\n状态: {status_text}"
            else:
                tooltip = base_text

            # 如果启用了定时运行，添加定时信息
            if hasattr(self, 'timer_enabled_checkbox') and self.timer_enabled_checkbox.isChecked():
                time_str = self.timer_time_edit.time().toString("HH:mm")
                timer_status = self.timer_status_label.text()
                tooltip += f"\n定时: {timer_status}"

            self.tray_icon.setToolTip(tooltip)

    def check_scheduled_time(self):
        """检查是否到达定时时间"""
        if not self.timer_enabled_checkbox.isChecked():
            return

        from datetime import datetime, time

        current_datetime = datetime.now()
        current_date = current_datetime.date()
        current_time = current_datetime.time()

        # 检查是否是新的一天，如果是则重置执行标志
        if current_date != self.last_check_date:
            self.timer_executed_today = False
            self.last_check_date = current_date
            self.logger.info("新的一天开始，重置定时执行标志")

        # 如果今天已经执行过，则不再执行
        if self.timer_executed_today:
            return

        # 获取设定的时间
        scheduled_qtime = self.timer_time_edit.time()
        scheduled_time = time(scheduled_qtime.hour(), scheduled_qtime.minute())

        # 调试信息
        current_time_str = current_time.strftime("%H:%M:%S")
        scheduled_time_str = scheduled_time.strftime("%H:%M")
        self.logger.debug(f"定时检查: 当前时间={current_time_str}, 设定时间={scheduled_time_str}")

        # 检查当前时间是否到达或超过设定时间（只比较小时和分钟）
        current_hm = time(current_time.hour, current_time.minute)
        scheduled_hm = time(scheduled_time.hour, scheduled_time.minute)

        if current_hm >= scheduled_hm:
            self.logger.info(f"到达定时时间，准备执行任务: {scheduled_time_str}")
            self.execute_scheduled_task()

    def execute_scheduled_task(self):
        """执行定时任务"""
        self.logger.info("定时任务触发，开始执行...")

        # 检查程序状态是否允许开始
        if self.current_state != ApplicationState.IDLE:
            self.logger.warning("定时任务执行失败：程序正在运行中")
            self.update_timer_status("错误：程序运行中")
            # 标记为已执行，避免重复检查
            self.timer_executed_today = True
            return

        # 执行开始任务
        self.logger.info("执行定时任务：自动开始处理URL")
        self.timer_executed_today = True
        self.update_timer_status("已执行")

        # 发送开始信号
        self.start_clicked.emit()

    def update_timer_status(self, status: str):
        """更新定时器状态显示"""
        self.timer_status_label.setText(status)

        # 根据状态设置颜色
        if "错误" in status:
            self.timer_status_label.setStyleSheet("color: red; font-style: italic;")
        elif "已执行" in status:
            self.timer_status_label.setStyleSheet("color: green; font-style: italic;")
        elif "等待中" in status:
            self.timer_status_label.setStyleSheet("color: blue; font-style: italic;")
        else:
            self.timer_status_label.setStyleSheet("color: gray; font-style: italic;")

        # 更新托盘提示
        self.update_tray_tooltip()



    def setup_shortcuts(self):
        """设置快捷键"""
        # Ctrl+O 打开文件
        QShortcut(QKeySequence.Open, self, self.on_file_select)

        # F5 开始
        QShortcut(QKeySequence('F5'), self, self.on_start_click)

        # F6 暂停
        QShortcut(QKeySequence('F6'), self, self.on_pause_click)

        # F7 停止
        QShortcut(QKeySequence('F7'), self, self.on_stop_click)

        # Esc 停止
        QShortcut(QKeySequence('Escape'), self, self.on_stop_click)
    
    def setup_ui(self):
        """设置用户界面"""
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # 创建各个组件
        self.create_file_selection_group(main_layout)
        self.create_settings_group(main_layout)
        self.create_control_group(main_layout)
        self.create_progress_group(main_layout)

        # 添加弹性空间
        main_layout.addStretch()
    
    def create_file_selection_group(self, parent_layout):
        """创建文件选择组"""
        group = QGroupBox("文件选择")
        group.setMinimumHeight(120)
        layout = QVBoxLayout(group)
        layout.setSpacing(15)
        layout.setContentsMargins(15, 30, 15, 25)

        # 创建水平布局用于按钮和文本框并排
        file_layout = QHBoxLayout()
        file_layout.setSpacing(10)

        # 文件选择按钮
        self.file_button = QPushButton("选择文件")
        self.file_button.setMinimumHeight(40)
        self.file_button.setMaximumWidth(120)
        self.file_button.setFont(QFont("", 10))
        file_layout.addWidget(self.file_button)

        # 文件路径显示文本框
        from PyQt5.QtWidgets import QLineEdit
        self.file_path_edit = QLineEdit()
        self.file_path_edit.setPlaceholderText("请选择URL文件...")
        self.file_path_edit.setReadOnly(True)
        self.file_path_edit.setMinimumHeight(40)
        self.file_path_edit.setFont(QFont("", 9))
        self.file_path_edit.setStyleSheet("""
            QLineEdit {
                background-color: #f9f9f9;
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 8px;
                color: #333;
            }
            QLineEdit:focus {
                border: 2px solid #4CAF50;
            }
        """)
        file_layout.addWidget(self.file_path_edit)

        layout.addLayout(file_layout)

        parent_layout.addWidget(group)
    
    def create_settings_group(self, parent_layout):
        """创建设置组"""
        group = QGroupBox("设置")
        group.setMinimumHeight(130)
        layout = QVBoxLayout(group)
        layout.setContentsMargins(15, 20, 15, 15)
        layout.setSpacing(12)

        # 第一行：时间间隔设置
        interval_layout = QHBoxLayout()
        interval_layout.setSpacing(10)

        label = QLabel("时间间隔:")
        label.setFont(QFont("", 10))
        label.setMinimumWidth(80)
        interval_layout.addWidget(label)

        self.interval_spinbox = QDoubleSpinBox()
        self.interval_spinbox.setRange(self.config.min_interval, self.config.max_interval)
        self.interval_spinbox.setValue(self.config.default_interval)
        self.interval_spinbox.setSuffix(" 秒")
        self.interval_spinbox.setDecimals(1)
        self.interval_spinbox.setSingleStep(0.5)
        self.interval_spinbox.setMinimumWidth(120)
        self.interval_spinbox.setMinimumHeight(30)
        self.interval_spinbox.setFont(QFont("", 10))
        interval_layout.addWidget(self.interval_spinbox)

        interval_layout.addStretch()
        layout.addLayout(interval_layout)

        # 第二行：定时运行设置
        timer_layout = QHBoxLayout()
        timer_layout.setSpacing(10)

        from PyQt5.QtWidgets import QCheckBox, QTimeEdit
        from PyQt5.QtCore import QTime

        # 定时运行开关
        self.timer_enabled_checkbox = QCheckBox("定时运行:")
        self.timer_enabled_checkbox.setFont(QFont("", 10))
        self.timer_enabled_checkbox.setMinimumWidth(80)
        timer_layout.addWidget(self.timer_enabled_checkbox)

        # 时间选择器
        self.timer_time_edit = QTimeEdit()
        self.timer_time_edit.setTime(QTime(6, 0))  # 默认早上6点
        self.timer_time_edit.setDisplayFormat("HH:mm")
        self.timer_time_edit.setMinimumWidth(80)
        self.timer_time_edit.setMinimumHeight(30)
        self.timer_time_edit.setFont(QFont("", 10))
        self.timer_time_edit.setEnabled(False)  # 初始禁用
        timer_layout.addWidget(self.timer_time_edit)

        # 状态显示
        self.timer_status_label = QLabel("未启用")
        self.timer_status_label.setFont(QFont("", 9))
        self.timer_status_label.setStyleSheet("color: gray; font-style: italic;")
        timer_layout.addWidget(self.timer_status_label)

        timer_layout.addStretch()
        layout.addLayout(timer_layout)

        parent_layout.addWidget(group)
    
    def create_control_group(self, parent_layout):
        """创建控制按钮组"""
        group = QGroupBox("控制")
        group.setMinimumHeight(110)
        layout = QHBoxLayout(group)
        layout.setContentsMargins(15, 20, 15, 15)
        layout.setSpacing(15)

        # 开始按钮
        self.start_button = QPushButton("开始")
        self.start_button.setMinimumHeight(45)
        self.start_button.setFont(QFont("", 11, QFont.Bold))
        self.start_button.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; border-radius: 5px; }")
        layout.addWidget(self.start_button)

        # 暂停按钮
        self.pause_button = QPushButton("暂停")
        self.pause_button.setMinimumHeight(45)
        self.pause_button.setFont(QFont("", 11, QFont.Bold))
        self.pause_button.setStyleSheet("QPushButton { background-color: #FF9800; color: white; font-weight: bold; border-radius: 5px; }")
        layout.addWidget(self.pause_button)

        # 停止按钮
        self.stop_button = QPushButton("停止")
        self.stop_button.setMinimumHeight(45)
        self.stop_button.setFont(QFont("", 11, QFont.Bold))
        self.stop_button.setStyleSheet("QPushButton { background-color: #F44336; color: white; font-weight: bold; border-radius: 5px; }")
        layout.addWidget(self.stop_button)

        parent_layout.addWidget(group)
    

    
    def create_progress_group(self, parent_layout):
        """创建进度显示组"""
        group = QGroupBox("进度")
        group.setMinimumHeight(140)
        layout = QVBoxLayout(group)
        layout.setContentsMargins(15, 25, 15, 20)
        layout.setSpacing(15)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setMinimumHeight(40)
        self.progress_bar.setFont(QFont("", 11, QFont.Bold))
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #cccccc;
                border-radius: 6px;
                text-align: center;
                font-weight: bold;
                background-color: #f5f5f5;
            }
            QProgressBar::chunk {
                background-color: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #66bb6a, stop:1 #4caf50);
                border-radius: 4px;
                margin: 1px;
            }
        """)
        layout.addWidget(self.progress_bar)

        # 进度文本
        self.progress_label = QLabel("0 / 0")
        self.progress_label.setAlignment(Qt.AlignCenter)
        self.progress_label.setFont(QFont("", 12, QFont.Bold))
        self.progress_label.setMinimumHeight(35)
        self.progress_label.setStyleSheet("color: #555; padding: 8px;")
        layout.addWidget(self.progress_label)

        parent_layout.addWidget(group)
    
    def connect_signals(self):
        """连接信号和槽"""
        self.file_button.clicked.connect(self.on_file_select)
        self.start_button.clicked.connect(self.on_start_click)
        self.pause_button.clicked.connect(self.on_pause_click)
        self.stop_button.clicked.connect(self.on_stop_click)
        self.interval_spinbox.valueChanged.connect(self.on_interval_changed)

        # 定时器相关信号
        self.timer_enabled_checkbox.toggled.connect(self.on_timer_enabled_changed)
        self.timer_time_edit.timeChanged.connect(self.on_timer_time_changed)

    def on_file_select(self):
        """处理文件选择"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择URL文件",
            "",
            self.config.get_file_filter()
        )

        if file_path:
            self.selected_file_path = file_path
            # 更新文本框显示完整路径
            self.file_path_edit.setText(file_path)
            self.file_selected.emit(file_path)
            self.update_ui_state()
            self.logger.info(f"用户选择文件: {file_path}")

    def on_start_click(self):
        """处理开始按钮点击"""
        self.start_clicked.emit()
        self.logger.info("用户点击开始按钮")

    def on_pause_click(self):
        """处理暂停按钮点击"""
        self.pause_clicked.emit()
        self.logger.info("用户点击暂停按钮")

    def on_stop_click(self):
        """处理停止按钮点击"""
        self.stop_clicked.emit()
        self.logger.info("用户点击停止按钮")

    def on_interval_changed(self, value: float):
        """处理时间间隔改变"""
        self.interval_changed.emit(value)
        self.logger.info(f"用户修改时间间隔: {value}秒")

    def on_timer_enabled_changed(self, enabled: bool):
        """处理定时器启用状态改变"""
        if enabled:
            # 检查是否已选择文件
            if not self.selected_file_path:
                # 弹出提醒
                self.show_error_message("提醒", "请先选择URL文件，然后再启用定时运行功能。")
                # 取消勾选
                self.timer_enabled_checkbox.setChecked(False)
                return

            # 获取设定的时间
            time_str = self.timer_time_edit.time().toString("HH:mm")
            self.update_timer_status(f"等待中 ({time_str})")
            self.logger.info(f"用户启用定时运行: {time_str}")
            self.timer_time_edit.setEnabled(True)
        else:
            self.update_timer_status("未启用")
            self.logger.info("用户禁用定时运行")
            self.timer_time_edit.setEnabled(False)

        self.timer_enabled_changed.emit(enabled)

    def on_timer_time_changed(self, qtime):
        """处理定时时间改变"""
        self.timer_time_changed.emit(qtime)

        if self.timer_enabled_checkbox.isChecked():
            time_str = qtime.toString("HH:mm")
            self.update_timer_status(f"等待中 ({time_str})")
            self.logger.info(f"用户修改定时时间: {time_str}")

    def update_status(self, status: str):
        """
        更新状态显示（窗口标题和托盘提示）

        Args:
            status: 状态文本
        """
        # 更新窗口标题显示状态
        base_title = self.config.window_title
        if status != str(ApplicationState.IDLE):
            self.setWindowTitle(f"{base_title} - {status}")
        else:
            self.setWindowTitle(base_title)

        # 更新托盘提示
        self.update_tray_tooltip(status)

    def update_current_url(self, url: str):
        """
        更新当前URL显示（已移除URL显示栏，此方法保留以兼容控制器）

        Args:
            url: 当前URL
        """
        # URL显示栏已移除，此方法不执行任何操作
        pass

    def update_progress(self, current: int, total: int):
        """
        更新进度显示

        Args:
            current: 当前进度
            total: 总数
        """
        self.progress_bar.setMaximum(total)
        self.progress_bar.setValue(current)
        self.progress_label.setText(f"{current} / {total}")

        if total > 0:
            percentage = (current / total) * 100
            self.progress_bar.setFormat(f"{percentage:.1f}%")
        else:
            self.progress_bar.setFormat("0%")

    def set_state(self, state: ApplicationState):
        """
        设置应用程序状态

        Args:
            state: 应用程序状态
        """
        self.current_state = state
        self.update_status(str(state))
        self.update_ui_state()

    def update_ui_state(self):
        """根据当前状态更新UI控件的启用/禁用状态"""
        has_file = bool(self.selected_file_path)

        if self.current_state == ApplicationState.IDLE:
            self.file_button.setEnabled(True)
            self.start_button.setEnabled(has_file)
            self.pause_button.setEnabled(False)
            self.stop_button.setEnabled(False)
            self.interval_spinbox.setEnabled(True)

        elif self.current_state == ApplicationState.RUNNING:
            self.file_button.setEnabled(False)
            self.start_button.setEnabled(False)
            self.pause_button.setEnabled(True)
            self.stop_button.setEnabled(True)
            self.interval_spinbox.setEnabled(True)

        elif self.current_state == ApplicationState.PAUSED:
            self.file_button.setEnabled(False)
            self.start_button.setEnabled(True)
            self.pause_button.setEnabled(False)
            self.stop_button.setEnabled(True)
            self.interval_spinbox.setEnabled(True)

        elif self.current_state == ApplicationState.STOPPED:
            self.file_button.setEnabled(True)
            self.start_button.setEnabled(has_file)
            self.pause_button.setEnabled(False)
            self.stop_button.setEnabled(False)
            self.interval_spinbox.setEnabled(True)

    def show_error_message(self, title: str, message: str):
        """
        显示错误消息对话框

        Args:
            title: 对话框标题
            message: 错误消息
        """
        QMessageBox.critical(self, title, message)

    def show_info_message(self, title: str, message: str):
        """
        显示信息消息对话框

        Args:
            title: 对话框标题
            message: 信息消息
        """
        QMessageBox.information(self, title, message)

    def get_interval(self) -> float:
        """
        获取当前设置的时间间隔

        Returns:
            float: 时间间隔（秒）
        """
        return self.interval_spinbox.value()

    def is_timer_enabled(self) -> bool:
        """
        获取定时器是否启用

        Returns:
            bool: 定时器是否启用
        """
        return self.timer_enabled_checkbox.isChecked()

    def get_timer_time(self):
        """
        获取设定的定时时间

        Returns:
            QTime: 设定的时间
        """
        return self.timer_time_edit.time()

    def closeEvent(self, event):
        """
        处理窗口关闭事件

        Args:
            event: 关闭事件
        """
        if self.current_state == ApplicationState.RUNNING:
            reply = QMessageBox.question(
                self,
                "确认退出",
                "程序正在运行中，确定要退出吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                self.stop_clicked.emit()  # 发送停止信号
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()

        self.logger.info("主窗口关闭")

    def set_window_style(self):
        """设置窗口样式"""
        # 设置现代化的样式
        style = """
        QMainWindow {
            background-color: #f5f5f5;
        }

        QGroupBox {
            font-weight: bold;
            border: 2px solid #cccccc;
            border-radius: 5px;
            margin-top: 1ex;
            padding-top: 10px;
        }

        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }

        QPushButton {
            border: 1px solid #cccccc;
            border-radius: 4px;
            padding: 5px;
            background-color: white;
        }

        QPushButton:hover {
            background-color: #e6e6e6;
        }

        QPushButton:pressed {
            background-color: #d4d4d4;
        }

        QPushButton:disabled {
            background-color: #f0f0f0;
            color: #999999;
        }

        QProgressBar {
            border: 1px solid #cccccc;
            border-radius: 4px;
            text-align: center;
        }

        QProgressBar::chunk {
            background-color: #4CAF50;
            border-radius: 3px;
        }
        """

        self.setStyleSheet(style)
