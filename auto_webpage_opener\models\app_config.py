"""
应用程序配置类
定义应用程序的默认配置和常量
"""

from dataclasses import dataclass
from typing import List, Tuple


@dataclass
class AppConfig:
    """应用程序配置类"""
    # 默认设置
    default_interval: float = 5.0  # 默认时间间隔（秒）
    min_interval: float = 0.5      # 最小时间间隔（秒）
    max_interval: float = 3600.0   # 最大时间间隔（秒）
    
    # 窗口设置
    window_title: str = "自动网页打开器"
    window_size: Tuple[int, int] = (650, 580)
    window_min_size: Tuple[int, int] = (600, 560)
    
    # 文件设置
    supported_extensions: List[str] = None
    default_encoding: str = "utf-8"
    fallback_encodings: List[str] = None
    
    # 日志设置
    log_file: str = "auto_webpage_opener.log"
    log_level: str = "INFO"
    log_format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # URL验证设置
    url_timeout: float = 5.0  # URL验证超时时间
    
    def __post_init__(self):
        """初始化后处理"""
        if self.supported_extensions is None:
            self.supported_extensions = [".txt"]
        
        if self.fallback_encodings is None:
            self.fallback_encodings = ["gbk", "gb2312", "latin-1"]
    
    @classmethod
    def get_default_config(cls) -> 'AppConfig':
        """获取默认配置"""
        return cls()
    
    def validate_interval(self, interval: float) -> bool:
        """验证时间间隔是否有效"""
        return self.min_interval <= interval <= self.max_interval
    
    def get_file_filter(self) -> str:
        """获取文件选择对话框的过滤器"""
        extensions = " ".join([f"*{ext}" for ext in self.supported_extensions])
        return f"文本文件 ({extensions});;所有文件 (*.*)"
