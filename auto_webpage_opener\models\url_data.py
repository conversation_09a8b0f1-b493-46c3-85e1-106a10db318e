"""
URL数据模型
管理URL列表和当前处理位置
"""

from dataclasses import dataclass
from typing import List, Optional


@dataclass
class URLData:
    """URL数据类"""
    urls: List[str]
    current_index: int = 0
    total_count: int = 0
    
    def __post_init__(self):
        """初始化后处理"""
        self.total_count = len(self.urls)
        if self.current_index >= self.total_count:
            self.current_index = 0
    
    def get_current_url(self) -> Optional[str]:
        """获取当前URL"""
        if 0 <= self.current_index < self.total_count:
            return self.urls[self.current_index]
        return None
    
    def get_next_url(self) -> Optional[str]:
        """获取下一个URL并移动索引"""
        if self.current_index < self.total_count:
            url = self.urls[self.current_index]
            self.current_index += 1
            return url
        return None
    
    def reset_position(self):
        """重置位置到开始"""
        self.current_index = 0
    
    def get_progress(self) -> tuple[int, int]:
        """获取进度信息 (当前位置, 总数)"""
        return (self.current_index, self.total_count)
    
    def is_finished(self) -> bool:
        """检查是否已完成所有URL"""
        return self.current_index >= self.total_count
    
    def get_remaining_count(self) -> int:
        """获取剩余URL数量"""
        return max(0, self.total_count - self.current_index)
