"""
浏览器打开器
负责调用系统默认浏览器打开URL
"""

import logging
import webbrowser
import subprocess
import sys
from typing import Optional

from ..utils.validators import URLValidator
from ..utils.error_handler import ErrorHandler


class BrowserOpener:
    """浏览器打开器类"""
    
    def __init__(self):
        """初始化浏览器打开器"""
        self.logger = logging.getLogger("auto_webpage_opener.services.browser_opener")
        self.error_handler = ErrorHandler(self.logger)
    
    @staticmethod
    def is_valid_url(url: str) -> bool:
        """
        检查URL是否有效
        
        Args:
            url: 要检查的URL
            
        Returns:
            bool: URL是否有效
        """
        return URLValidator.is_valid_url(url)
    
    def open_url(self, url: str) -> bool:
        """
        在默认浏览器中打开URL
        
        Args:
            url: 要打开的URL
            
        Returns:
            bool: 是否成功打开
        """
        if not url:
            self.logger.error("URL为空")
            return False
        
        # 验证URL格式
        if not self.is_valid_url(url):
            self.logger.error(f"无效的URL格式: {url}")
            return False
        
        try:
            # 尝试使用webbrowser模块打开URL
            success = webbrowser.open(url)
            
            if success:
                self.logger.info(f"成功打开URL: {url}")
                return True
            else:
                self.logger.warning(f"webbrowser.open返回False: {url}")
                # 尝试备用方法
                return self._open_url_fallback(url)
                
        except Exception as e:
            error_msg = self.error_handler.handle_browser_error(e)
            self.logger.error(f"打开URL失败: {url} - {error_msg}")
            # 尝试备用方法
            return self._open_url_fallback(url)
    
    def _open_url_fallback(self, url: str) -> bool:
        """
        备用的URL打开方法
        
        Args:
            url: 要打开的URL
            
        Returns:
            bool: 是否成功打开
        """
        try:
            if sys.platform.startswith('win'):
                # Windows系统
                subprocess.run(['start', url], shell=True, check=True)
            elif sys.platform.startswith('darwin'):
                # macOS系统
                subprocess.run(['open', url], check=True)
            elif sys.platform.startswith('linux'):
                # Linux系统
                subprocess.run(['xdg-open', url], check=True)
            else:
                self.logger.error(f"不支持的操作系统: {sys.platform}")
                return False
            
            self.logger.info(f"使用备用方法成功打开URL: {url}")
            return True
            
        except subprocess.CalledProcessError as e:
            self.logger.error(f"备用方法打开URL失败: {url} - {str(e)}")
            return False
        except Exception as e:
            self.logger.error(f"备用方法异常: {url} - {str(e)}")
            return False
    
    def test_browser_availability(self) -> bool:
        """
        测试浏览器是否可用

        Returns:
            bool: 浏览器是否可用
        """
        try:
            # 不实际打开URL，只检查webbrowser模块是否可用
            browser = webbrowser.get()
            if browser:
                self.logger.info("浏览器可用性测试通过")
                return True
            else:
                self.logger.warning("浏览器可用性测试失败")
                return False

        except Exception as e:
            self.logger.error(f"浏览器可用性测试异常: {str(e)}")
            return False
    
    def get_default_browser(self) -> Optional[str]:
        """
        获取默认浏览器信息
        
        Returns:
            Optional[str]: 默认浏览器名称，获取失败时返回None
        """
        try:
            # 尝试获取默认浏览器
            browser = webbrowser.get()
            browser_name = getattr(browser, 'name', 'Unknown')
            self.logger.info(f"默认浏览器: {browser_name}")
            return browser_name
            
        except Exception as e:
            self.logger.error(f"获取默认浏览器信息失败: {str(e)}")
            return None
    
    def open_multiple_urls(self, urls: list[str], delay: float = 0.5) -> tuple[int, int]:
        """
        批量打开多个URL
        
        Args:
            urls: URL列表
            delay: URL之间的延迟时间（秒）
            
        Returns:
            tuple[int, int]: (成功数量, 失败数量)
        """
        import time
        
        success_count = 0
        failure_count = 0
        
        for i, url in enumerate(urls):
            if i > 0 and delay > 0:
                time.sleep(delay)
            
            if self.open_url(url):
                success_count += 1
            else:
                failure_count += 1
        
        self.logger.info(f"批量打开URL完成: 成功 {success_count}, 失败 {failure_count}")
        return success_count, failure_count
