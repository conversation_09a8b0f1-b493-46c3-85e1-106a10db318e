"""
URL管理器
负责URL文件的读取、验证和管理
"""

import logging
from typing import List, Optional, Tuple
from pathlib import Path

from ..models.url_data import URLData
from ..models.app_config import AppConfig
from ..utils.validators import URLValidator
from ..utils.error_handler import ErrorHandler


class URLManager:
    """URL管理器类"""
    
    def __init__(self, config: Optional[AppConfig] = None):
        """
        初始化URL管理器
        
        Args:
            config: 应用程序配置
        """
        self.config = config or AppConfig.get_default_config()
        self.logger = logging.getLogger("auto_webpage_opener.services.url_manager")
        self.error_handler = ErrorHandler(self.logger)
        self.url_data: Optional[URLData] = None
        
    def load_urls_from_file(self, file_path: str) -> bool:
        """
        从文件加载URL列表
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 是否成功加载
        """
        try:
            # 检查文件是否存在
            path = Path(file_path)
            if not path.exists():
                raise FileNotFoundError(f"文件不存在: {file_path}")
            
            # 尝试不同编码读取文件
            content = self._read_file_with_encoding(file_path)
            if content is None:
                return False
            
            # 解析URL列表
            raw_urls = self._parse_urls_from_content(content)
            if not raw_urls:
                self.logger.warning(f"文件中没有找到有效的URL: {file_path}")
                return False
            
            # 验证URL
            valid_urls, invalid_urls = URLValidator.validate_url_list(raw_urls)
            
            if invalid_urls:
                self.logger.warning(f"发现 {len(invalid_urls)} 个无效URL: {invalid_urls[:5]}")
            
            if not valid_urls:
                self.logger.error("没有找到有效的URL")
                return False
            
            # 创建URL数据对象
            self.url_data = URLData(urls=valid_urls)
            
            self.logger.info(f"成功加载 {len(valid_urls)} 个有效URL")
            for url in valid_urls:
                self.logger.info(f"加载URL: {url}")
            
            return True
            
        except Exception as e:
            error_msg = self.error_handler.handle_file_error(e, file_path)
            return False
    
    def _read_file_with_encoding(self, file_path: str) -> Optional[str]:
        """
        使用多种编码尝试读取文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            Optional[str]: 文件内容，失败时返回None
        """
        encodings = [self.config.default_encoding] + self.config.fallback_encodings
        
        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as file:
                    content = file.read()
                    self.logger.debug(f"成功使用编码 {encoding} 读取文件: {file_path}")
                    return content
            except UnicodeDecodeError:
                self.logger.debug(f"编码 {encoding} 读取失败，尝试下一个编码")
                continue
            except Exception as e:
                self.logger.error(f"读取文件失败: {file_path} - {str(e)}")
                return None
        
        self.logger.error(f"所有编码都无法读取文件: {file_path}")
        return None
    
    def _parse_urls_from_content(self, content: str) -> List[str]:
        """
        从文件内容中解析URL列表
        
        Args:
            content: 文件内容
            
        Returns:
            List[str]: URL列表
        """
        urls = []
        lines = content.split('\n')
        
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            
            # 跳过空行和注释行
            if not line or line.startswith('#'):
                continue
            
            # 检查是否是URL（简单检查是否包含http）
            if 'http' in line.lower():
                # 清理URL
                cleaned_url = URLValidator.clean_url(line)
                if cleaned_url:
                    urls.append(cleaned_url)
                    self.logger.debug(f"第{line_num}行找到URL: {cleaned_url}")
        
        return urls
    
    def get_next_url(self) -> Optional[str]:
        """
        获取下一个URL
        
        Returns:
            Optional[str]: 下一个URL，如果没有则返回None
        """
        if not self.url_data:
            return None
        
        return self.url_data.get_next_url()
    
    def get_current_url(self) -> Optional[str]:
        """
        获取当前URL
        
        Returns:
            Optional[str]: 当前URL
        """
        if not self.url_data:
            return None
        
        return self.url_data.get_current_url()
    
    def reset_position(self):
        """重置URL位置到开始"""
        if self.url_data:
            self.url_data.reset_position()
            self.logger.info("URL位置已重置到开始")
    
    def get_progress(self) -> Tuple[int, int]:
        """
        获取进度信息
        
        Returns:
            Tuple[int, int]: (当前位置, 总数)
        """
        if not self.url_data:
            return (0, 0)
        
        return self.url_data.get_progress()
    
    def is_finished(self) -> bool:
        """
        检查是否已完成所有URL
        
        Returns:
            bool: 是否已完成
        """
        if not self.url_data:
            return True
        
        return self.url_data.is_finished()
    
    def get_url_count(self) -> int:
        """
        获取URL总数
        
        Returns:
            int: URL总数
        """
        if not self.url_data:
            return 0
        
        return self.url_data.total_count
    
    def get_remaining_count(self) -> int:
        """
        获取剩余URL数量
        
        Returns:
            int: 剩余URL数量
        """
        if not self.url_data:
            return 0
        
        return self.url_data.get_remaining_count()
    
    def validate_url(self, url: str) -> bool:
        """
        验证单个URL
        
        Args:
            url: 要验证的URL
            
        Returns:
            bool: URL是否有效
        """
        return URLValidator.is_valid_url(url)
