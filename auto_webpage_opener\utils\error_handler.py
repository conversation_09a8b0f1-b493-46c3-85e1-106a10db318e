"""
错误处理工具
提供统一的错误处理和日志记录功能
"""

import logging
import sys
from typing import Callable, Any, Optional
from PyQt5.QtWidgets import QMessageBox


def setup_logging(log_file: str = "auto_webpage_opener.log", 
                 log_level: str = "INFO",
                 log_format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s") -> logging.Logger:
    """
    设置日志记录
    
    Args:
        log_file: 日志文件路径
        log_level: 日志级别
        log_format: 日志格式
        
    Returns:
        配置好的logger对象
    """
    # 创建logger
    logger = logging.getLogger("auto_webpage_opener")
    logger.setLevel(getattr(logging, log_level.upper()))
    
    # 清除现有的处理器
    logger.handlers.clear()
    
    # 创建文件处理器
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(getattr(logging, log_level.upper()))
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.WARNING)

    # 设置控制台编码
    if hasattr(console_handler.stream, 'reconfigure'):
        console_handler.stream.reconfigure(encoding='utf-8')
    elif hasattr(console_handler.stream, 'buffer'):
        import codecs
        console_handler.stream = codecs.getwriter('utf-8')(console_handler.stream.buffer)
    
    # 创建格式器
    formatter = logging.Formatter(log_format)
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    # 添加处理器到logger
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger


def safe_execute(func: Callable, error_message: str, 
                show_dialog: bool = True, 
                logger: Optional[logging.Logger] = None) -> Any:
    """
    安全执行函数，捕获异常并处理
    
    Args:
        func: 要执行的函数
        error_message: 错误消息前缀
        show_dialog: 是否显示错误对话框
        logger: 日志记录器
        
    Returns:
        函数执行结果，异常时返回None
    """
    try:
        return func()
    except Exception as e:
        error_text = f"{error_message}: {str(e)}"
        
        # 记录日志
        if logger:
            logger.error(error_text)
        else:
            print(f"ERROR: {error_text}")
        
        # 显示错误对话框
        if show_dialog:
            try:
                QMessageBox.critical(None, "错误", error_text)
            except Exception:
                # 如果GUI不可用，只打印错误
                print(f"GUI ERROR: {error_text}")
        
        return None


class ErrorHandler:
    """错误处理器类"""
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger("auto_webpage_opener")
    
    def handle_file_error(self, error: Exception, file_path: str) -> str:
        """处理文件相关错误"""
        if isinstance(error, FileNotFoundError):
            message = f"文件不存在: {file_path}"
        elif isinstance(error, PermissionError):
            message = f"文件访问权限不足: {file_path}"
        elif isinstance(error, UnicodeDecodeError):
            message = f"文件编码格式不支持: {file_path}"
        else:
            message = f"文件操作失败: {file_path} - {str(error)}"
        
        self.logger.error(message)
        return message
    
    def handle_url_error(self, error: Exception, url: str) -> str:
        """处理URL相关错误"""
        message = f"URL处理失败: {url} - {str(error)}"
        self.logger.error(message)
        return message
    
    def handle_browser_error(self, error: Exception) -> str:
        """处理浏览器启动错误"""
        message = f"无法启动浏览器: {str(error)}"
        self.logger.error(message)
        return message
