"""
验证器工具
提供URL和其他数据的验证功能
"""

import re
from urllib.parse import urlparse
from typing import List


class URLValidator:
    """URL验证器"""
    
    # URL正则表达式模式
    URL_PATTERN = re.compile(
        r'^https?://'  # http:// 或 https://
        r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # 域名
        r'localhost|'  # localhost
        r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # IP地址
        r'(?::\d+)?'  # 可选端口
        r'(?:/?|[/?]\S+)$', re.IGNORECASE)
    
    @classmethod
    def is_valid_url(cls, url: str) -> bool:
        """
        验证URL格式是否有效
        
        Args:
            url: 要验证的URL字符串
            
        Returns:
            bool: URL是否有效
        """
        if not url or not isinstance(url, str):
            return False
        
        # 去除首尾空白字符
        url = url.strip()
        
        if not url:
            return False
        
        # 使用正则表达式验证
        if not cls.URL_PATTERN.match(url):
            return False
        
        # 使用urllib.parse进行进一步验证
        try:
            parsed = urlparse(url)
            return all([parsed.scheme, parsed.netloc])
        except Exception:
            return False
    
    @classmethod
    def validate_url_list(cls, urls: List[str]) -> tuple[List[str], List[str]]:
        """
        验证URL列表
        
        Args:
            urls: URL字符串列表
            
        Returns:
            tuple: (有效URL列表, 无效URL列表)
        """
        valid_urls = []
        invalid_urls = []
        
        for url in urls:
            if cls.is_valid_url(url):
                valid_urls.append(url.strip())
            else:
                invalid_urls.append(url.strip() if url else "空行")
        
        return valid_urls, invalid_urls
    
    @classmethod
    def clean_url(cls, url: str) -> str:
        """
        清理URL字符串
        
        Args:
            url: 原始URL字符串
            
        Returns:
            str: 清理后的URL字符串
        """
        if not url:
            return ""
        
        # 去除首尾空白字符
        url = url.strip()
        
        # 如果没有协议前缀，添加http://
        if url and not url.startswith(('http://', 'https://')):
            url = 'http://' + url
        
        return url


class IntervalValidator:
    """时间间隔验证器"""
    
    @staticmethod
    def is_valid_interval(interval: float, min_val: float = 0.5, max_val: float = 3600.0) -> bool:
        """
        验证时间间隔是否有效
        
        Args:
            interval: 时间间隔值
            min_val: 最小值
            max_val: 最大值
            
        Returns:
            bool: 时间间隔是否有效
        """
        try:
            return min_val <= float(interval) <= max_val
        except (ValueError, TypeError):
            return False
    
    @staticmethod
    def normalize_interval(interval: str, default: float = 5.0) -> float:
        """
        标准化时间间隔输入
        
        Args:
            interval: 时间间隔字符串
            default: 默认值
            
        Returns:
            float: 标准化后的时间间隔
        """
        try:
            value = float(interval)
            if IntervalValidator.is_valid_interval(value):
                return value
            else:
                return default
        except (ValueError, TypeError):
            return default
