2025-07-29 17:29:36,129 - auto_webpage_opener - INFO - 自动网页打开器应用程序初始化完成
2025-07-29 17:29:36,538 - auto_webpage_opener.controllers.app_controller - INFO - 应用程序控制器初始化完成
2025-07-29 17:29:36,538 - auto_webpage_opener - INFO - 应用程序组件初始化完成
2025-07-29 17:29:36,543 - auto_webpage_opener.controllers.app_controller - INFO - 开始加载文件: C:\Users\<USER>\AppData\Local\Temp\tmptytyz8fg.txt
2025-07-29 17:29:36,543 - auto_webpage_opener.services.url_manager - INFO - 成功加载 5 个有效URL
2025-07-29 17:29:36,543 - auto_webpage_opener.services.url_manager - INFO - 加载URL: http://www.example.com
2025-07-29 17:29:36,543 - auto_webpage_opener.services.url_manager - INFO - 加载URL: https://www.google.com
2025-07-29 17:29:36,544 - auto_webpage_opener.services.url_manager - INFO - 加载URL: http://www.baidu.com
2025-07-29 17:29:36,544 - auto_webpage_opener.services.url_manager - INFO - 加载URL: https://www.github.com
2025-07-29 17:29:36,544 - auto_webpage_opener.services.url_manager - INFO - 加载URL: http://www.stackoverflow.com
2025-07-29 17:29:36,544 - auto_webpage_opener.controllers.app_controller - INFO - 文件加载成功，共 5 个URL
2025-07-29 17:29:39,358 - auto_webpage_opener.services.browser_opener - INFO - 浏览器可用性测试通过
2025-07-29 17:29:39,358 - auto_webpage_opener - INFO - 开始清理应用程序资源
2025-07-29 17:29:39,358 - auto_webpage_opener.controllers.app_controller - INFO - 应用程序控制器资源清理完成
2025-07-29 17:29:39,360 - auto_webpage_opener.gui.main_window - INFO - 主窗口关闭
2025-07-29 17:29:39,360 - auto_webpage_opener - INFO - 应用程序资源清理完成
2025-07-29 17:29:39,361 - auto_webpage_opener - INFO - 自动网页打开器应用程序初始化完成
2025-07-29 17:29:39,362 - auto_webpage_opener.controllers.app_controller - INFO - 应用程序控制器初始化完成
2025-07-29 17:29:39,364 - auto_webpage_opener - INFO - 应用程序组件初始化完成
2025-07-29 17:29:39,365 - auto_webpage_opener.controllers.app_controller - INFO - 开始加载文件: nonexistent_file.txt
2025-07-29 17:29:39,365 - auto_webpage_opener.services.url_manager - ERROR - 文件不存在: nonexistent_file.txt
2025-07-29 17:29:39,365 - auto_webpage_opener.controllers.app_controller - ERROR - 文件加载失败: nonexistent_file.txt
2025-07-29 17:29:43,616 - auto_webpage_opener.controllers.app_controller - INFO - 开始加载文件: C:\Users\<USER>\AppData\Local\Temp\tmp1djaq_4n.txt
2025-07-29 17:29:43,616 - auto_webpage_opener.services.url_manager - WARNING - 文件中没有找到有效的URL: C:\Users\<USER>\AppData\Local\Temp\tmp1djaq_4n.txt
2025-07-29 17:29:43,616 - auto_webpage_opener.controllers.app_controller - ERROR - 文件加载失败: C:\Users\<USER>\AppData\Local\Temp\tmp1djaq_4n.txt
2025-07-29 17:29:44,518 - auto_webpage_opener - INFO - 开始清理应用程序资源
2025-07-29 17:29:44,520 - auto_webpage_opener.controllers.app_controller - INFO - 应用程序控制器资源清理完成
2025-07-29 17:29:44,520 - auto_webpage_opener.gui.main_window - INFO - 主窗口关闭
2025-07-29 17:29:44,520 - auto_webpage_opener - INFO - 应用程序资源清理完成
2025-07-29 17:29:44,521 - auto_webpage_opener - INFO - 自动网页打开器应用程序初始化完成
2025-07-29 17:29:44,523 - auto_webpage_opener.controllers.app_controller - INFO - 应用程序控制器初始化完成
2025-07-29 17:29:44,533 - auto_webpage_opener - INFO - 应用程序组件初始化完成
2025-07-29 17:29:44,534 - auto_webpage_opener.controllers.app_controller - INFO - 开始加载文件: C:\Users\<USER>\AppData\Local\Temp\tmpfte_74w_.txt
2025-07-29 17:29:44,535 - auto_webpage_opener.services.url_manager - INFO - 成功加载 3 个有效URL
2025-07-29 17:29:44,535 - auto_webpage_opener.services.url_manager - INFO - 加载URL: http://www.example.com
2025-07-29 17:29:44,535 - auto_webpage_opener.services.url_manager - INFO - 加载URL: https://www.google.com
2025-07-29 17:29:44,535 - auto_webpage_opener.services.url_manager - INFO - 加载URL: http://www.baidu.com
2025-07-29 17:29:44,535 - auto_webpage_opener.controllers.app_controller - INFO - 文件加载成功，共 3 个URL
2025-07-29 17:29:45,390 - auto_webpage_opener - INFO - 开始清理应用程序资源
2025-07-29 17:29:45,390 - auto_webpage_opener.controllers.app_controller - INFO - 应用程序控制器资源清理完成
2025-07-29 17:29:45,390 - auto_webpage_opener.gui.main_window - INFO - 主窗口关闭
2025-07-29 17:29:45,390 - auto_webpage_opener - INFO - 应用程序资源清理完成
2025-07-29 17:32:53,623 - auto_webpage_opener - INFO - 自动网页打开器应用程序初始化完成
2025-07-29 17:32:53,849 - auto_webpage_opener.controllers.app_controller - INFO - 应用程序控制器初始化完成
2025-07-29 17:32:53,849 - auto_webpage_opener - INFO - 应用程序组件初始化完成
2025-07-29 17:32:53,849 - auto_webpage_opener - INFO - 自动网页打开器应用程序初始化完成
2025-07-29 17:32:53,852 - auto_webpage_opener.controllers.app_controller - INFO - 应用程序控制器初始化完成
2025-07-29 17:32:53,852 - auto_webpage_opener - INFO - 应用程序组件初始化完成
2025-07-29 17:32:53,852 - auto_webpage_opener.controllers.app_controller - INFO - 开始加载文件: nonexistent_file.txt
2025-07-29 17:32:53,852 - auto_webpage_opener.services.url_manager - ERROR - 文件不存在: nonexistent_file.txt
2025-07-29 17:32:53,852 - auto_webpage_opener.controllers.app_controller - ERROR - 文件加载失败: nonexistent_file.txt
2025-07-29 17:32:58,564 - auto_webpage_opener - INFO - 自动网页打开器应用程序初始化完成
2025-07-29 17:32:58,569 - auto_webpage_opener.controllers.app_controller - INFO - 应用程序控制器初始化完成
2025-07-29 17:32:58,577 - auto_webpage_opener - INFO - 应用程序组件初始化完成
2025-07-29 17:32:58,581 - auto_webpage_opener.controllers.app_controller - INFO - 开始加载文件: C:\Users\<USER>\AppData\Local\Temp\tmpvkiguf2g.txt
2025-07-29 17:32:58,582 - auto_webpage_opener.services.url_manager - INFO - 成功加载 3 个有效URL
2025-07-29 17:32:58,582 - auto_webpage_opener.services.url_manager - INFO - 加载URL: http://www.example.com
2025-07-29 17:32:58,582 - auto_webpage_opener.services.url_manager - INFO - 加载URL: https://www.google.com
2025-07-29 17:32:58,582 - auto_webpage_opener.services.url_manager - INFO - 加载URL: http://www.baidu.com
2025-07-29 17:32:58,582 - auto_webpage_opener.controllers.app_controller - INFO - 文件加载成功，共 3 个URL
2025-07-29 17:32:59,804 - auto_webpage_opener - ERROR - 未处理的异常: UnicodeEncodeError: 'gbk' codec can't encode character '\u2717' in position 8: illegal multibyte sequence
Traceback (most recent call last):
  File "E:\开发项目\待开发项目\自动打开网页\env_test\test_integration.py", line 217, in <module>
    sys.exit(main())
  File "E:\开发项目\待开发项目\自动打开网页\env_test\test_integration.py", line 212, in main
    print("部分测试失败！ ✗")
UnicodeEncodeError: 'gbk' codec can't encode character '\u2717' in position 8: illegal multibyte sequence
2025-07-29 17:35:01,045 - auto_webpage_opener - INFO - 自动网页打开器应用程序初始化完成
2025-07-29 17:35:01,230 - auto_webpage_opener.controllers.app_controller - INFO - 应用程序控制器初始化完成
2025-07-29 17:35:01,230 - auto_webpage_opener - INFO - 应用程序组件初始化完成
2025-07-29 17:35:01,230 - auto_webpage_opener.controllers.app_controller - INFO - 开始加载文件: C:\Users\<USER>\AppData\Local\Temp\tmp9ud8uidb.txt
2025-07-29 17:35:01,230 - auto_webpage_opener.services.url_manager - INFO - 成功加载 5 个有效URL
2025-07-29 17:35:01,230 - auto_webpage_opener.services.url_manager - INFO - 加载URL: http://www.example.com
2025-07-29 17:35:01,230 - auto_webpage_opener.services.url_manager - INFO - 加载URL: https://www.google.com
2025-07-29 17:35:01,230 - auto_webpage_opener.services.url_manager - INFO - 加载URL: http://www.baidu.com
2025-07-29 17:35:01,230 - auto_webpage_opener.services.url_manager - INFO - 加载URL: https://www.github.com
2025-07-29 17:35:01,230 - auto_webpage_opener.services.url_manager - INFO - 加载URL: http://www.stackoverflow.com
2025-07-29 17:35:01,230 - auto_webpage_opener.controllers.app_controller - INFO - 文件加载成功，共 5 个URL
2025-07-29 17:35:03,196 - auto_webpage_opener.services.browser_opener - INFO - 浏览器可用性测试通过
2025-07-29 17:35:03,203 - auto_webpage_opener - INFO - 开始清理应用程序资源
2025-07-29 17:35:03,203 - auto_webpage_opener.controllers.app_controller - INFO - 应用程序控制器资源清理完成
2025-07-29 17:35:03,203 - auto_webpage_opener.gui.main_window - INFO - 主窗口关闭
2025-07-29 17:35:03,203 - auto_webpage_opener - INFO - 应用程序资源清理完成
2025-07-29 17:35:03,203 - auto_webpage_opener - INFO - 自动网页打开器应用程序初始化完成
2025-07-29 17:35:03,206 - auto_webpage_opener.controllers.app_controller - INFO - 应用程序控制器初始化完成
2025-07-29 17:35:03,215 - auto_webpage_opener - INFO - 应用程序组件初始化完成
2025-07-29 17:35:03,215 - auto_webpage_opener.controllers.app_controller - INFO - 开始加载文件: nonexistent_file.txt
2025-07-29 17:35:03,215 - auto_webpage_opener.services.url_manager - ERROR - 文件不存在: nonexistent_file.txt
2025-07-29 17:35:03,215 - auto_webpage_opener.controllers.app_controller - ERROR - 文件加载失败: nonexistent_file.txt
2025-07-29 17:35:07,889 - auto_webpage_opener.controllers.app_controller - INFO - 开始加载文件: C:\Users\<USER>\AppData\Local\Temp\tmpluwjkb2t.txt
2025-07-29 17:35:07,890 - auto_webpage_opener.services.url_manager - WARNING - 文件中没有找到有效的URL: C:\Users\<USER>\AppData\Local\Temp\tmpluwjkb2t.txt
2025-07-29 17:35:07,890 - auto_webpage_opener.controllers.app_controller - ERROR - 文件加载失败: C:\Users\<USER>\AppData\Local\Temp\tmpluwjkb2t.txt
2025-07-29 17:35:09,230 - auto_webpage_opener - INFO - 开始清理应用程序资源
2025-07-29 17:35:09,230 - auto_webpage_opener.controllers.app_controller - INFO - 应用程序控制器资源清理完成
2025-07-29 17:35:09,230 - auto_webpage_opener.gui.main_window - INFO - 主窗口关闭
2025-07-29 17:35:09,230 - auto_webpage_opener - INFO - 应用程序资源清理完成
2025-07-29 17:35:09,234 - auto_webpage_opener - INFO - 自动网页打开器应用程序初始化完成
2025-07-29 17:35:09,237 - auto_webpage_opener.controllers.app_controller - INFO - 应用程序控制器初始化完成
2025-07-29 17:35:09,247 - auto_webpage_opener - INFO - 应用程序组件初始化完成
2025-07-29 17:35:09,248 - auto_webpage_opener.controllers.app_controller - INFO - 开始加载文件: C:\Users\<USER>\AppData\Local\Temp\tmpjc43uvj9.txt
2025-07-29 17:35:09,248 - auto_webpage_opener.services.url_manager - INFO - 成功加载 3 个有效URL
2025-07-29 17:35:09,248 - auto_webpage_opener.services.url_manager - INFO - 加载URL: http://www.example.com
2025-07-29 17:35:09,248 - auto_webpage_opener.services.url_manager - INFO - 加载URL: https://www.google.com
2025-07-29 17:35:09,248 - auto_webpage_opener.services.url_manager - INFO - 加载URL: http://www.baidu.com
2025-07-29 17:35:09,249 - auto_webpage_opener.controllers.app_controller - INFO - 文件加载成功，共 3 个URL
2025-07-29 17:35:10,168 - auto_webpage_opener - INFO - 开始清理应用程序资源
2025-07-29 17:35:10,168 - auto_webpage_opener.controllers.app_controller - INFO - 应用程序控制器资源清理完成
2025-07-29 17:35:10,168 - auto_webpage_opener.gui.main_window - INFO - 主窗口关闭
2025-07-29 17:35:10,168 - auto_webpage_opener - INFO - 应用程序资源清理完成
2025-08-05 15:37:34,281 - auto_webpage_opener - INFO - 自动网页打开器应用程序初始化完成
2025-08-05 15:37:34,315 - auto_webpage_opener.gui.main_window - INFO - 成功设置窗口图标: E:\开发项目\待开发项目\自动打开网页\env_test\..\auto_webpage_opener\gui\..\..\package\app_icon.png
2025-08-05 15:37:34,563 - auto_webpage_opener.gui.main_window - INFO - 系统托盘已设置
2025-08-05 15:37:34,566 - auto_webpage_opener.controllers.app_controller - INFO - 应用程序控制器初始化完成
2025-08-05 15:37:34,566 - auto_webpage_opener - INFO - 应用程序组件初始化完成
2025-08-05 15:37:36,672 - auto_webpage_opener.gui.main_window - INFO - 用户选择退出程序
2025-08-05 15:37:36,675 - auto_webpage_opener - INFO - 开始清理应用程序资源
2025-08-05 15:37:36,675 - auto_webpage_opener.controllers.app_controller - INFO - 应用程序控制器资源清理完成
2025-08-05 15:37:36,675 - auto_webpage_opener.gui.main_window - INFO - 主窗口关闭
2025-08-05 15:37:36,692 - auto_webpage_opener - INFO - 应用程序资源清理完成
