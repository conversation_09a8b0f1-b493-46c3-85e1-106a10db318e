"""
调试定时器功能
"""

import sys
import os
from datetime import datetime, time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTime, QTimer
from auto_webpage_opener.app import AutoWebpageOpenerApp
from auto_webpage_opener.models.app_config import AppConfig


def debug_timer():
    """调试定时器功能"""
    print("开始调试定时器功能...")
    
    # 创建应用程序
    app = QApplication(sys.argv)
    
    # 创建配置
    config = AppConfig.get_default_config()
    
    # 创建应用程序实例
    auto_app = AutoWebpageOpenerApp(config)
    auto_app.initialize_components()
    
    # 获取主窗口
    main_window = auto_app.get_main_window()
    
    if main_window:
        # 显示窗口
        main_window.show()
        
        # 设置一个接近当前时间的测试时间（当前时间+2分钟）
        current_time = datetime.now()
        test_time = current_time.replace(second=0, microsecond=0)
        test_time = test_time.replace(minute=test_time.minute + 2)
        
        # 设置定时器
        qtime = QTime(test_time.hour, test_time.minute)
        main_window.timer_time_edit.setTime(qtime)
        main_window.timer_enabled_checkbox.setChecked(True)
        
        print(f"当前时间: {current_time.strftime('%H:%M:%S')}")
        print(f"设置定时时间: {test_time.strftime('%H:%M')}")
        print("定时器已启用，等待自动执行...")
        
        # 创建一个调试定时器，每10秒打印一次状态
        debug_timer = QTimer()
        
        def print_debug_info():
            now = datetime.now()
            print(f"[{now.strftime('%H:%M:%S')}] 当前状态检查:")
            print(f"  - 定时器启用: {main_window.timer_enabled_checkbox.isChecked()}")
            print(f"  - 设定时间: {main_window.timer_time_edit.time().toString('HH:mm')}")
            print(f"  - 今日已执行: {main_window.timer_executed_today}")
            print(f"  - 状态显示: {main_window.timer_status_label.text()}")
            print(f"  - 选择的文件: {main_window.selected_file_path}")
            print(f"  - 程序状态: {main_window.current_state}")
            print("---")
        
        debug_timer.timeout.connect(print_debug_info)
        debug_timer.start(10000)  # 每10秒打印一次
        
        # 立即打印一次初始状态
        print_debug_info()
        
        print("\n请注意观察:")
        print("1. 每10秒会打印当前状态")
        print("2. 请选择一个URL文件")
        print("3. 观察到达设定时间时是否自动执行")
        print("4. 查看日志文件 auto_webpage_opener.log 获取详细信息")
        
        # 运行应用程序
        sys.exit(app.exec_())
    else:
        print("错误: 无法创建主窗口")
        return 1


if __name__ == "__main__":
    debug_timer()
