"""
运行所有测试的主程序
"""

import sys
import os
import unittest
import subprocess

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))


def run_unit_tests():
    """运行单元测试"""
    print("=" * 50)
    print("运行单元测试")
    print("=" * 50)
    
    test_files = [
        'test_url_manager.py',
        'test_browser_opener.py'
    ]
    
    all_passed = True
    
    for test_file in test_files:
        print(f"\n运行 {test_file}...")
        try:
            result = subprocess.run([sys.executable, test_file], 
                                  capture_output=True, text=True, 
                                  cwd=os.path.dirname(__file__))
            
            if result.returncode == 0:
                print(f"[通过] {test_file}")
                print(result.stdout)
            else:
                print(f"[失败] {test_file}")
                print(result.stderr)
                all_passed = False
                
        except Exception as e:
            print(f"✗ 运行 {test_file} 时发生错误: {str(e)}")
            all_passed = False
    
    return all_passed


def run_integration_tests():
    """运行集成测试"""
    print("\n" + "=" * 50)
    print("运行集成测试")
    print("=" * 50)
    
    try:
        result = subprocess.run([sys.executable, 'test_integration.py'], 
                              capture_output=True, text=True,
                              cwd=os.path.dirname(__file__))
        
        if result.returncode == 0:
            print("[通过] 集成测试")
            print(result.stdout)
            return True
        else:
            print("[失败] 集成测试")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"✗ 运行集成测试时发生错误: {str(e)}")
        return False


def run_performance_tests():
    """运行性能测试"""
    print("\n" + "=" * 50)
    print("运行性能测试")
    print("=" * 50)
    
    try:
        # 导入必要的模块
        from auto_webpage_opener.services.url_manager import URLManager
        from auto_webpage_opener.services.browser_opener import BrowserOpener
        from auto_webpage_opener.models.app_config import AppConfig
        import tempfile
        import time
        
        # 创建大量URL的测试文件
        large_content = "\n".join([f"http://example{i}.com" for i in range(1000)])
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
            f.write(large_content)
            large_file = f.name
        
        # 测试大文件加载性能
        config = AppConfig.get_default_config()
        url_manager = URLManager(config)
        
        start_time = time.time()
        success = url_manager.load_urls_from_file(large_file)
        load_time = time.time() - start_time
        
        if success:
            url_count = url_manager.get_url_count()
            print(f"[通过] 大文件加载测试")
            print(f"  - 加载了 {url_count} 个URL")
            print(f"  - 耗时: {load_time:.3f} 秒")

            if load_time < 5.0:  # 5秒内完成认为性能良好
                print("[通过] 性能测试")
                performance_passed = True
            else:
                print("[警告] 性能可能需要优化")
                performance_passed = True  # 仍然算通过，只是提醒
        else:
            print("✗ 大文件加载失败")
            performance_passed = False
        
        # 清理
        try:
            os.unlink(large_file)
        except Exception:
            pass
        
        return performance_passed
        
    except Exception as e:
        print(f"✗ 性能测试发生错误: {str(e)}")
        return False


def generate_test_report(unit_passed, integration_passed, performance_passed):
    """生成测试报告"""
    print("\n" + "=" * 50)
    print("测试报告")
    print("=" * 50)
    
    total_tests = 3
    passed_tests = sum([unit_passed, integration_passed, performance_passed])
    
    print(f"单元测试:     {'通过' if unit_passed else '失败'}")
    print(f"集成测试:     {'通过' if integration_passed else '失败'}")
    print(f"性能测试:     {'通过' if performance_passed else '失败'}")
    print("-" * 30)
    print(f"总计:        {passed_tests}/{total_tests} 通过")
    
    if passed_tests == total_tests:
        print("\n[成功] 所有测试都通过了！项目可以发布。")
        return True
    else:
        print(f"\n[警告] 有 {total_tests - passed_tests} 个测试失败，请检查并修复问题。")
        return False


def main():
    """主函数"""
    print("自动网页打开器 - 完整测试套件")
    print("开始运行所有测试...")
    
    # 运行各类测试
    unit_passed = run_unit_tests()
    integration_passed = run_integration_tests()
    performance_passed = run_performance_tests()
    
    # 生成报告
    all_passed = generate_test_report(unit_passed, integration_passed, performance_passed)
    
    # 返回适当的退出代码
    return 0 if all_passed else 1


if __name__ == "__main__":
    sys.exit(main())
