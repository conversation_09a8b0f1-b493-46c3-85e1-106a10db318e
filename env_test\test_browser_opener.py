"""
BrowserOpener类的单元测试
"""

import unittest
import os
from unittest.mock import patch, MagicMock

# 添加项目根目录到Python路径
import sys
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from auto_webpage_opener.services.browser_opener import BrowserOpener


class TestBrowserOpener(unittest.TestCase):
    """BrowserOpener测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.browser_opener = BrowserOpener()
    
    def test_is_valid_url(self):
        """测试URL验证功能"""
        # 有效URL
        valid_urls = [
            "http://www.example.com",
            "https://www.google.com",
            "http://localhost:8080",
            "https://***********:3000/path"
        ]
        
        for url in valid_urls:
            self.assertTrue(BrowserOpener.is_valid_url(url), f"应该是有效URL: {url}")
        
        # 无效URL
        invalid_urls = [
            "",
            "not_a_url",
            "ftp://example.com",
            "www.example.com",  # 缺少协议
            "http://",
            None
        ]
        
        for url in invalid_urls:
            self.assertFalse(BrowserOpener.is_valid_url(url), f"应该是无效URL: {url}")
    
    @patch('webbrowser.open')
    def test_open_url_success(self, mock_webbrowser_open):
        """测试成功打开URL"""
        mock_webbrowser_open.return_value = True
        
        result = self.browser_opener.open_url("http://www.example.com")
        self.assertTrue(result)
        mock_webbrowser_open.assert_called_once_with("http://www.example.com")
    
    @patch('webbrowser.open')
    def test_open_url_invalid(self, mock_webbrowser_open):
        """测试打开无效URL"""
        result = self.browser_opener.open_url("invalid_url")
        self.assertFalse(result)
        mock_webbrowser_open.assert_not_called()
    
    @patch('webbrowser.open')
    def test_open_url_empty(self, mock_webbrowser_open):
        """测试打开空URL"""
        result = self.browser_opener.open_url("")
        self.assertFalse(result)
        mock_webbrowser_open.assert_not_called()
    
    @patch('webbrowser.open')
    @patch('subprocess.run')
    def test_open_url_fallback(self, mock_subprocess_run, mock_webbrowser_open):
        """测试备用打开方法"""
        # webbrowser.open失败
        mock_webbrowser_open.return_value = False
        mock_subprocess_run.return_value = MagicMock()
        
        result = self.browser_opener.open_url("http://www.example.com")
        
        # 应该调用webbrowser.open
        mock_webbrowser_open.assert_called_once_with("http://www.example.com")
        # 应该调用备用方法
        mock_subprocess_run.assert_called_once()
    
    @patch('webbrowser.open')
    def test_open_url_exception(self, mock_webbrowser_open):
        """测试打开URL时发生异常"""
        mock_webbrowser_open.side_effect = Exception("Browser error")
        
        with patch.object(self.browser_opener, '_open_url_fallback', return_value=False):
            result = self.browser_opener.open_url("http://www.example.com")
            self.assertFalse(result)
    
    @patch('webbrowser.open')
    def test_test_browser_availability(self, mock_webbrowser_open):
        """测试浏览器可用性检查"""
        mock_webbrowser_open.return_value = True
        
        result = self.browser_opener.test_browser_availability()
        self.assertTrue(result)
        mock_webbrowser_open.assert_called_once_with("about:blank")
    
    @patch('webbrowser.get')
    def test_get_default_browser(self, mock_webbrowser_get):
        """测试获取默认浏览器"""
        mock_browser = MagicMock()
        mock_browser.name = "chrome"
        mock_webbrowser_get.return_value = mock_browser
        
        result = self.browser_opener.get_default_browser()
        self.assertEqual(result, "chrome")
    
    def test_open_multiple_urls(self):
        """测试批量打开URL"""
        urls = [
            "http://www.example.com",
            "https://www.google.com",
            "invalid_url",
            "http://www.baidu.com"
        ]
        
        with patch.object(self.browser_opener, 'open_url') as mock_open_url:
            # 模拟前3个成功，最后1个失败
            mock_open_url.side_effect = [True, True, False, True]
            
            success_count, failure_count = self.browser_opener.open_multiple_urls(urls, delay=0)
            
            self.assertEqual(success_count, 3)
            self.assertEqual(failure_count, 1)
            self.assertEqual(mock_open_url.call_count, 4)


if __name__ == '__main__':
    unittest.main()
