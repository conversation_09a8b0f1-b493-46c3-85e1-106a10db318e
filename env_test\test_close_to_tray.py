"""
测试点击关闭按钮隐藏到托盘功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from PyQt5.QtWidgets import QApplication
from auto_webpage_opener.app import AutoWebpageOpenerApp
from auto_webpage_opener.models.app_config import AppConfig


def test_close_to_tray():
    """测试点击关闭按钮隐藏到托盘功能"""
    print("测试点击关闭按钮隐藏到托盘功能...")
    
    # 创建应用程序
    app = QApplication(sys.argv)
    
    # 设置不要在最后一个窗口关闭时自动退出
    app.setQuitOnLastWindowClosed(False)
    
    # 创建配置
    config = AppConfig.get_default_config()
    
    # 创建应用程序实例
    auto_app = AutoWebpageOpenerApp(config)
    auto_app.initialize_components()
    
    # 获取主窗口
    main_window = auto_app.get_main_window()
    
    if main_window:
        # 显示窗口
        main_window.show()
        
        print("✅ 修复完成！现在测试关闭按钮功能：")
        print("\n🎯 测试步骤：")
        print("1. 点击窗口右上角的 ❌ 关闭按钮")
        print("2. 窗口应该隐藏到系统托盘（不退出程序）")
        print("3. 查看系统托盘区域是否有地球🌍图标")
        print("4. 应该显示提示消息：'程序已最小化到系统托盘'")
        print("5. 双击托盘图标可以重新显示窗口")
        print("6. 右键托盘图标选择'退出程序'才会真正退出")
        
        print("\n🔧 技术修复：")
        print("- 设置了 app.setQuitOnLastWindowClosed(False)")
        print("- 重写了 closeEvent 方法")
        print("- 添加了系统托盘支持")
        print("- 区分了隐藏窗口和退出程序")
        
        print("\n💡 现在的行为：")
        print("- 点击 ❌ → 隐藏到托盘")
        print("- 托盘菜单'退出程序' → 真正退出")
        print("- 双击托盘图标 → 显示/隐藏窗口")
        
        # 运行应用程序
        sys.exit(app.exec_())
    else:
        print("错误: 无法创建主窗口")
        return 1


if __name__ == "__main__":
    test_close_to_tray()
