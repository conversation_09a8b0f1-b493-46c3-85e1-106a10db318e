"""
测试最终版本的GUI界面
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from PyQt5.QtWidgets import QApplication
from auto_webpage_opener.app import AutoWebpageOpenerApp
from auto_webpage_opener.models.app_config import AppConfig


def test_final_gui():
    """测试最终版本的GUI界面"""
    print("启动最终版本的GUI界面...")
    
    # 创建应用程序
    app = QApplication(sys.argv)
    
    # 创建配置
    config = AppConfig.get_default_config()
    
    # 创建应用程序实例
    auto_app = AutoWebpageOpenerApp(config)
    auto_app.initialize_components()
    
    # 获取主窗口
    main_window = auto_app.get_main_window()
    
    if main_window:
        # 显示窗口
        main_window.show()
        
        print("最终版本GUI界面特点：")
        print("✓ 简洁的界面设计，无菜单栏")
        print("✓ 文件选择组：高度100px")
        print("✓ 设置组：高度80px")
        print("✓ 控制组：高度90px，按钮高度45px")
        print("✓ 进度和状态组：高度200px")
        print("  - 当前URL显示：高度50px")
        print("  - 进度条：高度40px")
        print("  - 进度文本：高度35px")
        print("✓ 窗口大小：650x520px")
        print("✓ 状态显示在窗口标题中")
        print("\n界面应该不再有重叠问题，请验证所有组件显示正常。")
        
        # 运行应用程序
        sys.exit(app.exec_())
    else:
        print("错误: 无法创建主窗口")
        return 1


if __name__ == "__main__":
    test_final_gui()
