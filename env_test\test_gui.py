"""
GUI界面测试程序
用于测试主窗口界面的显示和基本功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from PyQt5.QtWidgets import QApplication
from auto_webpage_opener.gui.main_window import MainWindow
from auto_webpage_opener.models.app_config import AppConfig
from auto_webpage_opener.models.app_state import ApplicationState
from auto_webpage_opener.utils.error_handler import setup_logging


def test_main_window():
    """测试主窗口"""
    # 设置日志
    logger = setup_logging()
    
    # 创建应用程序
    app = QApplication(sys.argv)
    
    # 创建配置
    config = AppConfig.get_default_config()
    
    # 创建主窗口
    window = MainWindow(config)
    
    # 连接信号进行测试
    def on_file_selected(file_path):
        print(f"文件选择信号: {file_path}")
        # 模拟加载成功
        window.update_progress(0, 10)
    
    def on_start_clicked():
        print("开始按钮点击信号")
        window.set_state(ApplicationState.RUNNING)
        window.update_current_url("http://www.example.com")
        window.update_progress(1, 10)
    
    def on_pause_clicked():
        print("暂停按钮点击信号")
        window.set_state(ApplicationState.PAUSED)
    
    def on_stop_clicked():
        print("停止按钮点击信号")
        window.set_state(ApplicationState.STOPPED)
        window.update_current_url("")
        window.update_progress(0, 10)
    
    def on_interval_changed(value):
        print(f"时间间隔改变信号: {value}秒")
    
    # 连接信号
    window.file_selected.connect(on_file_selected)
    window.start_clicked.connect(on_start_clicked)
    window.pause_clicked.connect(on_pause_clicked)
    window.stop_clicked.connect(on_stop_clicked)
    window.interval_changed.connect(on_interval_changed)
    
    # 显示窗口
    window.show()
    
    print("GUI测试窗口已启动")
    print("请测试以下功能：")
    print("1. 点击'选择URL文件'按钮")
    print("2. 修改时间间隔设置")
    print("3. 点击'开始'按钮")
    print("4. 点击'暂停'按钮")
    print("5. 点击'停止'按钮")
    print("6. 关闭窗口")
    
    # 运行应用程序
    sys.exit(app.exec_())


if __name__ == '__main__':
    test_main_window()
