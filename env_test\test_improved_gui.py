"""
测试改进后的GUI界面
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from PyQt5.QtWidgets import QApplication
from auto_webpage_opener.app import AutoWebpageOpenerApp
from auto_webpage_opener.models.app_config import AppConfig


def test_improved_gui():
    """测试改进后的GUI界面"""
    print("启动改进后的GUI界面测试...")
    
    # 创建应用程序
    app = QApplication(sys.argv)
    
    # 创建配置
    config = AppConfig.get_default_config()
    
    # 创建应用程序实例
    auto_app = AutoWebpageOpenerApp(config)
    auto_app.initialize_components()
    
    # 获取主窗口
    main_window = auto_app.get_main_window()
    
    if main_window:
        # 显示窗口
        main_window.show()
        
        print("GUI界面已启动，请检查以下改进：")
        print("1. 窗口大小是否合适（700x600）")
        print("2. 各个组件是否有足够的间距")
        print("3. 按钮高度是否合适")
        print("4. 字体大小是否清晰")
        print("5. 整体布局是否美观")
        
        # 运行应用程序
        sys.exit(app.exec_())
    else:
        print("错误: 无法创建主窗口")
        return 1


if __name__ == "__main__":
    test_improved_gui()
