"""
集成测试程序
测试完整的应用程序功能流程
"""

import sys
import os
import tempfile
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from auto_webpage_opener.app import AutoWebpageOpenerApp
from auto_webpage_opener.models.app_config import AppConfig


def create_test_url_file() -> str:
    """创建测试用的URL文件"""
    content = """
# 测试URL文件
http://www.example.com
https://www.google.com
http://www.baidu.com
https://www.github.com
http://www.stackoverflow.com
"""
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
        f.write(content)
        return f.name


def test_complete_workflow():
    """测试完整的工作流程"""
    print("开始集成测试...")
    
    try:
        # 创建测试配置
        config = AppConfig.get_default_config()
        config.default_interval = 2.0  # 设置较短的间隔用于测试
        
        # 创建应用程序
        app = AutoWebpageOpenerApp(config)
        app.initialize_components()
        
        # 获取组件
        main_window = app.get_main_window()
        app_controller = app.get_app_controller()
        
        if not main_window or not app_controller:
            print("ERROR: 无法获取应用程序组件")
            return False
        
        print("✓ 应用程序组件初始化成功")
        
        # 创建测试URL文件
        test_file = create_test_url_file()
        print(f"✓ 创建测试文件: {test_file}")
        
        # 测试文件加载
        app_controller.load_file(test_file)
        
        # 检查URL是否加载成功
        url_count = app_controller.url_manager.get_url_count()
        if url_count > 0:
            print(f"✓ 文件加载成功，共 {url_count} 个URL")
        else:
            print("ERROR: 文件加载失败")
            return False
        
        # 测试浏览器可用性
        if app_controller.browser_opener.test_browser_availability():
            print("✓ 浏览器可用性测试通过")
        else:
            print("WARNING: 浏览器可用性测试失败")
        
        # 清理测试文件
        try:
            os.unlink(test_file)
            print("✓ 清理测试文件")
        except Exception:
            pass
        
        # 清理应用程序
        app.cleanup()
        print("✓ 应用程序清理完成")
        
        print("集成测试完成 - 所有测试通过！")
        return True
        
    except Exception as e:
        print(f"ERROR: 集成测试失败 - {str(e)}")
        return False


def test_error_handling():
    """测试错误处理"""
    print("\n开始错误处理测试...")
    
    try:
        config = AppConfig.get_default_config()
        app = AutoWebpageOpenerApp(config)
        app.initialize_components()
        
        app_controller = app.get_app_controller()
        
        # 测试加载不存在的文件
        app_controller.load_file("nonexistent_file.txt")
        print("✓ 不存在文件的错误处理测试通过")
        
        # 测试加载空文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
            f.write("")
            empty_file = f.name
        
        app_controller.load_file(empty_file)
        print("✓ 空文件的错误处理测试通过")
        
        # 清理
        try:
            os.unlink(empty_file)
        except Exception:
            pass
        
        app.cleanup()
        print("✓ 错误处理测试完成")
        return True
        
    except Exception as e:
        print(f"ERROR: 错误处理测试失败 - {str(e)}")
        return False


def test_url_validation():
    """测试URL验证功能"""
    print("\n开始URL验证测试...")
    
    try:
        config = AppConfig.get_default_config()
        app = AutoWebpageOpenerApp(config)
        app.initialize_components()
        
        app_controller = app.get_app_controller()
        
        # 创建包含混合内容的测试文件
        mixed_content = """
# 测试文件 - 包含有效和无效URL
http://www.example.com
这不是一个URL
https://www.google.com
invalid_url
ftp://not_supported.com
http://www.baidu.com
        """
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
            f.write(mixed_content)
            mixed_file = f.name
        
        # 加载文件
        app_controller.load_file(mixed_file)
        
        # 检查只加载了有效的URL
        url_count = app_controller.url_manager.get_url_count()
        if url_count == 3:  # 应该只有3个有效的http/https URL
            print(f"✓ URL验证测试通过，有效URL数量: {url_count}")
        else:
            print(f"WARNING: URL验证可能有问题，URL数量: {url_count}")
        
        # 清理
        try:
            os.unlink(mixed_file)
        except Exception:
            pass
        
        app.cleanup()
        print("✓ URL验证测试完成")
        return True
        
    except Exception as e:
        print(f"ERROR: URL验证测试失败 - {str(e)}")
        return False


def main():
    """主测试函数"""
    print("=" * 50)
    print("自动网页打开器 - 集成测试")
    print("=" * 50)
    
    all_passed = True
    
    # 运行各项测试
    tests = [
        ("完整工作流程测试", test_complete_workflow),
        ("错误处理测试", test_error_handling),
        ("URL验证测试", test_url_validation),
    ]
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        if not test_func():
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("所有集成测试通过！ ✓")
        return 0
    else:
        print("部分测试失败！ ✗")
        return 1


if __name__ == "__main__":
    sys.exit(main())
