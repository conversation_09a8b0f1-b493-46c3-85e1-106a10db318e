"""
测试修复后的程序，确保启动时不会弹出about链接错误
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from PyQt5.QtWidgets import QApplication
from auto_webpage_opener.app import AutoWebpageOpenerApp
from auto_webpage_opener.models.app_config import AppConfig


def test_no_popup():
    """测试程序启动时不会弹出about链接错误"""
    print("测试程序启动，检查是否还有弹窗...")
    
    # 创建应用程序
    app = QApplication(sys.argv)
    
    # 创建配置
    config = AppConfig.get_default_config()
    
    # 创建应用程序实例
    auto_app = AutoWebpageOpenerApp(config)
    auto_app.initialize_components()
    
    # 获取主窗口
    main_window = auto_app.get_main_window()
    
    if main_window:
        # 显示窗口
        main_window.show()
        
        print("程序已启动，请检查：")
        print("1. 是否还有'无法打开此about链接'的弹窗")
        print("2. 窗口是否正常显示地球图标")
        print("3. 界面布局是否正确")
        print("4. 各个功能按钮是否可用")
        
        # 运行应用程序
        sys.exit(app.exec_())
    else:
        print("错误: 无法创建主窗口")
        return 1


if __name__ == "__main__":
    test_no_popup()
