"""
测试退出功能修复
验证QApplication导入问题是否已解决
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from auto_webpage_opener.app import AutoWebpageOpenerApp
from auto_webpage_opener.models.app_config import AppConfig


def test_quit_functionality():
    """测试退出功能"""
    print("测试退出功能修复...")
    
    try:
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 创建配置
        config = AppConfig.get_default_config()
        
        # 创建应用程序实例
        auto_app = AutoWebpageOpenerApp(config)
        auto_app.initialize_components()
        
        # 获取主窗口
        main_window = auto_app.get_main_window()
        
        if main_window:
            print("✓ 主窗口创建成功")
            
            # 显示窗口
            main_window.show()
            print("✓ 主窗口显示成功")
            
            # 测试quit_application方法是否可以正常调用
            try:
                # 设置一个定时器来自动退出，避免程序一直运行
                def auto_quit():
                    print("✓ 开始测试退出功能...")
                    try:
                        main_window.quit_application()
                        print("✓ quit_application方法调用成功，没有NameError")
                    except NameError as e:
                        print(f"✗ NameError仍然存在: {e}")
                        return False
                    except Exception as e:
                        print(f"✓ 其他异常（这是正常的）: {e}")
                    
                    # 正常退出
                    app.quit()
                    return True
                
                # 2秒后自动测试退出功能
                QTimer.singleShot(2000, auto_quit)
                
                # 运行应用程序
                app.exec_()
                
                print("✓ 退出功能测试完成")
                return True
                
            except Exception as e:
                print(f"✗ 测试过程中发生错误: {e}")
                return False
        else:
            print("✗ 主窗口创建失败")
            return False
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False


def test_import_fix():
    """测试导入修复"""
    print("测试QApplication导入修复...")
    
    try:
        # 直接导入主窗口模块
        from auto_webpage_opener.gui.main_window import MainWindow
        
        # 检查QApplication是否在模块中可用
        import auto_webpage_opener.gui.main_window as main_window_module
        
        if hasattr(main_window_module, 'QApplication'):
            print("✓ QApplication已正确导入到main_window模块")
            return True
        else:
            print("✗ QApplication未在main_window模块中找到")
            return False
            
    except ImportError as e:
        print(f"✗ 导入测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("=" * 50)
    print("退出功能修复测试")
    print("=" * 50)
    
    all_passed = True
    
    # 测试导入修复
    print("\n1. 测试QApplication导入修复:")
    if not test_import_fix():
        all_passed = False
    
    # 测试退出功能
    print("\n2. 测试退出功能:")
    if not test_quit_functionality():
        all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("所有测试通过！退出功能修复成功 ✓")
        return 0
    else:
        print("部分测试失败！ ✗")
        return 1


if __name__ == "__main__":
    sys.exit(main())
