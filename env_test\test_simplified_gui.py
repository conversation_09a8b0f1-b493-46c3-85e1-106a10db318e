"""
测试简化后的GUI界面
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from PyQt5.QtWidgets import QApplication
from auto_webpage_opener.app import AutoWebpageOpenerApp
from auto_webpage_opener.models.app_config import AppConfig


def test_simplified_gui():
    """测试简化后的GUI界面"""
    print("启动简化后的GUI界面测试...")
    
    # 创建应用程序
    app = QApplication(sys.argv)
    
    # 创建配置
    config = AppConfig.get_default_config()
    
    # 创建应用程序实例
    auto_app = AutoWebpageOpenerApp(config)
    auto_app.initialize_components()
    
    # 获取主窗口
    main_window = auto_app.get_main_window()
    
    if main_window:
        # 显示窗口
        main_window.show()
        
        print("简化后的GUI界面已启动，特点：")
        print("1. 移除了菜单栏，界面更简洁")
        print("2. 移除了独立的状态组")
        print("3. 将当前URL显示整合到进度组中")
        print("4. 窗口大小调整为650x480，更紧凑")
        print("5. 状态信息显示在窗口标题中")
        print("\n请测试基本功能是否正常工作。")
        
        # 运行应用程序
        sys.exit(app.exec_())
    else:
        print("错误: 无法创建主窗口")
        return 1


if __name__ == "__main__":
    test_simplified_gui()
