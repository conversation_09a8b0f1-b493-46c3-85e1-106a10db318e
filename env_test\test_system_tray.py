"""
测试系统托盘功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from PyQt5.QtWidgets import QApplication
from auto_webpage_opener.app import AutoWebpageOpenerApp
from auto_webpage_opener.models.app_config import AppConfig


def test_system_tray():
    """测试系统托盘功能"""
    print("测试系统托盘功能...")
    
    # 创建应用程序
    app = QApplication(sys.argv)
    
    # 创建配置
    config = AppConfig.get_default_config()
    
    # 创建应用程序实例
    auto_app = AutoWebpageOpenerApp(config)
    auto_app.initialize_components()
    
    # 获取主窗口
    main_window = auto_app.get_main_window()
    
    if main_window:
        # 显示窗口
        main_window.show()
        
        print("系统托盘功能已添加！")
        print("\n🎯 新增功能说明：")
        print("✅ 点击右上角关闭按钮 → 隐藏到系统托盘（不退出程序）")
        print("✅ 双击托盘图标 → 显示/隐藏窗口")
        print("✅ 右键托盘图标 → 显示功能菜单")
        print("✅ 托盘提示显示程序状态和定时信息")
        print("✅ 首次隐藏时显示提示消息")
        
        print("\n🔧 托盘菜单功能：")
        print("- 显示窗口 / 隐藏窗口")
        print("- 开始 / 暂停 / 停止")
        print("- 退出程序（真正退出）")
        
        print("\n📋 测试项目：")
        print("1. 点击窗口右上角的 ❌ 按钮")
        print("   → 应该隐藏到托盘并显示提示消息")
        print("2. 查看系统托盘区域是否有地球图标")
        print("3. 双击托盘图标")
        print("   → 应该重新显示窗口")
        print("4. 右键点击托盘图标")
        print("   → 应该显示功能菜单")
        print("5. 选择文件并启用定时运行")
        print("   → 托盘提示应该显示定时信息")
        print("6. 使用托盘菜单的'退出程序'")
        print("   → 应该完全退出程序")
        
        print("\n💡 使用场景：")
        print("- 设置定时运行后，可以隐藏窗口让程序在后台运行")
        print("- 程序会在设定时间自动执行，无需显示窗口")
        print("- 通过托盘图标可以随时查看程序状态")
        
        # 运行应用程序
        sys.exit(app.exec_())
    else:
        print("错误: 无法创建主窗口")
        return 1


if __name__ == "__main__":
    test_system_tray()
