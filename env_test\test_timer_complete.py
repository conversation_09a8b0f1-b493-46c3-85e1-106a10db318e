"""
完整测试定时器功能（包含文件选择）
"""

import sys
import os
import tempfile
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTime, QTimer
from auto_webpage_opener.app import AutoWebpageOpenerApp
from auto_webpage_opener.models.app_config import AppConfig


def create_test_url_file():
    """创建测试URL文件"""
    content = """# 测试URL文件
http://www.example.com
https://www.baidu.com
http://www.google.com
"""
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
        f.write(content)
        return f.name


def test_timer_complete():
    """完整测试定时器功能"""
    print("开始完整测试定时器功能...")
    
    # 创建应用程序
    app = QApplication(sys.argv)
    
    # 创建配置
    config = AppConfig.get_default_config()
    
    # 创建应用程序实例
    auto_app = AutoWebpageOpenerApp(config)
    auto_app.initialize_components()
    
    # 获取主窗口
    main_window = auto_app.get_main_window()
    
    if main_window:
        # 显示窗口
        main_window.show()
        
        # 创建测试URL文件
        test_file = create_test_url_file()
        print(f"创建测试文件: {test_file}")
        
        # 自动选择文件
        main_window.selected_file_path = test_file
        main_window.file_path_edit.setText(test_file)
        main_window.update_ui_state()
        print("已自动选择测试文件")
        
        # 设置一个接近当前时间的测试时间（当前时间+1分钟）
        current_time = datetime.now()
        test_time = current_time.replace(second=0, microsecond=0)
        test_time = test_time.replace(minute=test_time.minute + 1)
        
        # 设置定时器
        qtime = QTime(test_time.hour, test_time.minute)
        main_window.timer_time_edit.setTime(qtime)
        main_window.timer_enabled_checkbox.setChecked(True)
        
        print(f"当前时间: {current_time.strftime('%H:%M:%S')}")
        print(f"设置定时时间: {test_time.strftime('%H:%M')}")
        print("定时器已启用，文件已选择，等待自动执行...")
        
        # 创建一个调试定时器，每5秒打印一次状态
        debug_timer = QTimer()
        
        def print_debug_info():
            now = datetime.now()
            print(f"[{now.strftime('%H:%M:%S')}] 状态检查:")
            print(f"  - 定时器启用: {main_window.timer_enabled_checkbox.isChecked()}")
            print(f"  - 设定时间: {main_window.timer_time_edit.time().toString('HH:mm')}")
            print(f"  - 今日已执行: {main_window.timer_executed_today}")
            print(f"  - 状态显示: {main_window.timer_status_label.text()}")
            print(f"  - 文件已选择: {'是' if main_window.selected_file_path else '否'}")
            print(f"  - 程序状态: {main_window.current_state}")
            
            # 如果已执行，显示成功信息
            if main_window.timer_executed_today and "已执行" in main_window.timer_status_label.text():
                print("🎉 定时任务执行成功！")
                print("可以观察浏览器是否自动打开了URL")
            
            print("---")
        
        debug_timer.timeout.connect(print_debug_info)
        debug_timer.start(5000)  # 每5秒打印一次
        
        # 立即打印一次初始状态
        print_debug_info()
        
        print("\n测试说明:")
        print("1. 程序会在1分钟后自动开始处理URL")
        print("2. 每5秒会打印当前状态")
        print("3. 观察浏览器是否自动打开")
        print("4. 查看状态是否变为'已执行'")
        print("5. 查看日志文件获取详细信息")
        
        # 运行应用程序
        try:
            sys.exit(app.exec_())
        finally:
            # 清理测试文件
            try:
                os.unlink(test_file)
                print(f"清理测试文件: {test_file}")
            except:
                pass
    else:
        print("错误: 无法创建主窗口")
        return 1


if __name__ == "__main__":
    test_timer_complete()
