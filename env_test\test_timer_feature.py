"""
测试定时运行功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTime
from auto_webpage_opener.app import AutoWebpageOpenerApp
from auto_webpage_opener.models.app_config import AppConfig


def test_timer_feature():
    """测试定时运行功能"""
    print("测试定时运行功能...")
    
    # 创建应用程序
    app = QApplication(sys.argv)
    
    # 创建配置
    config = AppConfig.get_default_config()
    
    # 创建应用程序实例
    auto_app = AutoWebpageOpenerApp(config)
    auto_app.initialize_components()
    
    # 获取主窗口
    main_window = auto_app.get_main_window()
    
    if main_window:
        # 显示窗口
        main_window.show()
        
        print("定时运行功能已添加！")
        print("\n新增功能说明：")
        print("📅 定时运行设置：")
        print("  - 定时运行开关：启用/禁用定时功能")
        print("  - 时间选择器：设置每日运行时间（默认06:00）")
        print("  - 状态显示：显示定时器当前状态")
        print("\n🔧 功能特点：")
        print("  - 每分钟检查一次时间")
        print("  - 每日自动重置执行标志")
        print("  - 自动检查文件和程序状态")
        print("  - 详细的日志记录")
        print("\n⚠️ 使用注意：")
        print("  - 程序需要保持运行状态")
        print("  - 需要先选择URL文件")
        print("  - 每天只会自动执行一次")
        print("\n请测试以下功能：")
        print("1. 勾选'定时运行'复选框")
        print("2. 设置一个接近当前时间的时间（如当前时间+2分钟）")
        print("3. 选择一个URL文件")
        print("4. 观察状态变化和自动执行")
        
        # 运行应用程序
        sys.exit(app.exec_())
    else:
        print("错误: 无法创建主窗口")
        return 1


if __name__ == "__main__":
    test_timer_feature()
