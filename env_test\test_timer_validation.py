"""
测试定时器文件验证功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from PyQt5.QtWidgets import QApplication
from auto_webpage_opener.app import AutoWebpageOpenerApp
from auto_webpage_opener.models.app_config import AppConfig


def test_timer_validation():
    """测试定时器文件验证功能"""
    print("测试定时器文件验证功能...")
    
    # 创建应用程序
    app = QApplication(sys.argv)
    
    # 创建配置
    config = AppConfig.get_default_config()
    
    # 创建应用程序实例
    auto_app = AutoWebpageOpenerApp(config)
    auto_app.initialize_components()
    
    # 获取主窗口
    main_window = auto_app.get_main_window()
    
    if main_window:
        # 显示窗口
        main_window.show()
        
        print("定时器文件验证功能已完善！")
        print("\n功能说明：")
        print("✅ 当用户勾选'定时运行'时：")
        print("   - 如果没有选择文件，会弹出提醒")
        print("   - 自动取消勾选状态")
        print("   - 提示用户先选择URL文件")
        print("\n✅ 当用户已选择文件后：")
        print("   - 可以正常启用定时运行")
        print("   - 显示等待状态和设定时间")
        print("   - 到达时间自动执行")
        print("\n请测试以下场景：")
        print("1. 直接勾选'定时运行' -> 应该弹出提醒并取消勾选")
        print("2. 先选择文件，再勾选'定时运行' -> 应该正常启用")
        print("3. 设置接近当前时间的时间测试自动执行")
        
        # 运行应用程序
        sys.exit(app.exec_())
    else:
        print("错误: 无法创建主窗口")
        return 1


if __name__ == "__main__":
    test_timer_validation()
