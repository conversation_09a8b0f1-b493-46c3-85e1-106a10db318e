"""
URLManager类的单元测试
"""

import unittest
import tempfile
import os
from pathlib import Path

# 添加项目根目录到Python路径
import sys
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from auto_webpage_opener.services.url_manager import URLManager
from auto_webpage_opener.models.app_config import AppConfig


class TestURLManager(unittest.TestCase):
    """URLManager测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.config = AppConfig.get_default_config()
        self.url_manager = URLManager(self.config)
        
        # 创建临时测试文件
        self.temp_dir = tempfile.mkdtemp()
        
    def tearDown(self):
        """测试后清理"""
        # 清理临时文件
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def create_test_file(self, filename: str, content: str) -> str:
        """创建测试文件"""
        file_path = os.path.join(self.temp_dir, filename)
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        return file_path
    
    def test_load_valid_urls(self):
        """测试加载有效URL"""
        content = """
        http://www.example.com
        https://www.google.com
        http://www.baidu.com
        """
        file_path = self.create_test_file("valid_urls.txt", content)
        
        result = self.url_manager.load_urls_from_file(file_path)
        self.assertTrue(result)
        self.assertEqual(self.url_manager.get_url_count(), 3)
    
    def test_load_mixed_urls(self):
        """测试加载混合内容（包含无效URL）"""
        content = """
        http://www.example.com
        这不是一个URL
        https://www.google.com
        # 这是注释
        
        invalid_url
        http://www.baidu.com
        """
        file_path = self.create_test_file("mixed_urls.txt", content)
        
        result = self.url_manager.load_urls_from_file(file_path)
        self.assertTrue(result)
        self.assertEqual(self.url_manager.get_url_count(), 3)
    
    def test_load_empty_file(self):
        """测试加载空文件"""
        content = ""
        file_path = self.create_test_file("empty.txt", content)
        
        result = self.url_manager.load_urls_from_file(file_path)
        self.assertFalse(result)
    
    def test_load_nonexistent_file(self):
        """测试加载不存在的文件"""
        result = self.url_manager.load_urls_from_file("nonexistent.txt")
        self.assertFalse(result)
    
    def test_url_navigation(self):
        """测试URL导航功能"""
        content = """
        http://www.example.com
        https://www.google.com
        http://www.baidu.com
        """
        file_path = self.create_test_file("nav_test.txt", content)
        
        self.url_manager.load_urls_from_file(file_path)
        
        # 测试获取下一个URL
        url1 = self.url_manager.get_next_url()
        self.assertEqual(url1, "http://www.example.com")
        
        url2 = self.url_manager.get_next_url()
        self.assertEqual(url2, "https://www.google.com")
        
        url3 = self.url_manager.get_next_url()
        self.assertEqual(url3, "http://www.baidu.com")
        
        # 测试是否完成
        self.assertTrue(self.url_manager.is_finished())
        
        # 测试重置
        self.url_manager.reset_position()
        self.assertFalse(self.url_manager.is_finished())
        
        current_url = self.url_manager.get_current_url()
        self.assertEqual(current_url, "http://www.example.com")
    
    def test_progress_tracking(self):
        """测试进度跟踪"""
        content = """
        http://www.example.com
        https://www.google.com
        http://www.baidu.com
        """
        file_path = self.create_test_file("progress_test.txt", content)
        
        self.url_manager.load_urls_from_file(file_path)
        
        # 初始进度
        current, total = self.url_manager.get_progress()
        self.assertEqual(current, 0)
        self.assertEqual(total, 3)
        
        # 获取第一个URL后的进度
        self.url_manager.get_next_url()
        current, total = self.url_manager.get_progress()
        self.assertEqual(current, 1)
        self.assertEqual(total, 3)
        
        # 剩余数量
        remaining = self.url_manager.get_remaining_count()
        self.assertEqual(remaining, 2)


if __name__ == '__main__':
    unittest.main()
