#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动网页打开器 - 主程序入口
一个用于按时间间隔自动打开网址列表的桌面应用程序

使用方法:
    python main.py

作者: Auto Webpage Opener Team
版本: 1.0.0
"""

import sys
import os

# 确保项目根目录在Python路径中
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

try:
    from auto_webpage_opener.app import main
    
    if __name__ == "__main__":
        # 设置控制台编码为UTF-8（Windows系统）
        if sys.platform.startswith('win'):
            try:
                import locale
                locale.setlocale(locale.LC_ALL, 'zh_CN.UTF-8')
            except locale.Error:
                pass
        
        # 运行主程序
        main()
        
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保所有依赖包已正确安装。")
    print("运行: pip install -r requirements.txt")
    sys.exit(1)
except Exception as e:
    print(f"程序启动失败: {e}")
    sys.exit(1)
