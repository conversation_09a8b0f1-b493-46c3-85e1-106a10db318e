"""
使用PyInstaller打包为可执行文件的脚本
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def build_executable():
    """构建可执行文件"""
    print("开始构建自动网页打开器可执行文件...")

    # 项目根目录
    project_root = Path(__file__).parent.parent
    main_script = project_root / "main.py"

    if not main_script.exists():
        print(f"错误: 找不到主脚本文件 {main_script}")
        return False

    # 检查并创建图标
    icon_path = Path(__file__).parent / "app_icon.ico"
    if not icon_path.exists():
        print("图标文件不存在，尝试创建...")
        try:
            # 运行图标创建脚本
            result = subprocess.run([sys.executable, str(Path(__file__).parent / "create_icon.py")],
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print("图标创建成功")
            else:
                print(f"图标创建失败: {result.stderr}")
        except Exception as e:
            print(f"创建图标时发生错误: {e}")
    
    # PyInstaller命令
    cmd = [
        "pyinstaller",
        "--onefile",                    # 打包成单个文件
        "--windowed",                   # Windows下不显示控制台窗口
        "--name=自动网页打开器",          # 可执行文件名称
        "--distpath=package/dist",      # 输出目录
        "--workpath=package/build",     # 临时文件目录
        "--specpath=package",           # spec文件目录
        "--clean",                      # 清理临时文件
        str(main_script)
    ]
    
    # 添加图标（如果存在）
    icon_path = project_root / "package" / "app_icon.ico"
    if icon_path.exists():
        cmd.extend(["--icon", str(icon_path)])
        print(f"使用图标文件: {icon_path}")
    else:
        print("警告: 未找到图标文件，将使用默认图标")
    
    # 添加隐藏导入
    hidden_imports = [
        "PyQt5.QtCore",
        "PyQt5.QtGui", 
        "PyQt5.QtWidgets",
        "auto_webpage_opener.gui.main_window",
        "auto_webpage_opener.services.url_manager",
        "auto_webpage_opener.services.browser_opener",
        "auto_webpage_opener.controllers.app_controller",
        "auto_webpage_opener.controllers.url_worker"
    ]
    
    for module in hidden_imports:
        cmd.extend(["--hidden-import", module])
    
    try:
        print("执行PyInstaller命令...")
        print(" ".join(cmd))
        
        result = subprocess.run(cmd, cwd=project_root, check=True, 
                              capture_output=True, text=True, encoding='utf-8')
        
        print("构建成功！")
        print(f"可执行文件位置: {project_root}/package/dist/自动网页打开器.exe")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"构建失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False
    except FileNotFoundError:
        print("错误: 未找到PyInstaller，请先安装:")
        print("pip install pyinstaller")
        return False

def create_installer_script():
    """创建安装脚本"""
    installer_content = '''@echo off
echo 自动网页打开器安装程序
echo.

set "INSTALL_DIR=%USERPROFILE%\\Desktop\\自动网页打开器"

echo 正在创建安装目录...
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

echo 正在复制文件...
copy "自动网页打开器.exe" "%INSTALL_DIR%\\"
copy "README.txt" "%INSTALL_DIR%\\" 2>nul
copy "示例URL文件.txt" "%INSTALL_DIR%\\" 2>nul

echo.
echo 安装完成！
echo 程序已安装到: %INSTALL_DIR%
echo.
echo 创建桌面快捷方式...
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\\Desktop\\自动网页打开器.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\\自动网页打开器.exe'; $Shortcut.Save()"

echo.
echo 安装完成！您可以在桌面找到快捷方式。
pause
'''
    
    installer_path = Path(__file__).parent / "dist" / "install.bat"
    installer_path.parent.mkdir(exist_ok=True)
    
    with open(installer_path, 'w', encoding='gbk') as f:
        f.write(installer_content)
    
    print(f"安装脚本已创建: {installer_path}")

def create_readme():
    """创建说明文件"""
    readme_content = """自动网页打开器 v1.0.0

这是一个用于按时间间隔自动打开网址列表的桌面应用程序。

功能特点：
- 支持从文本文件加载URL列表
- 可自定义时间间隔（0.5秒到1小时）
- 支持开始、暂停、停止控制
- 实时进度显示
- 错误处理和日志记录
- 现代化的图形界面

使用方法：
1. 运行"自动网页打开器.exe"
2. 点击"选择URL文件"按钮，选择包含网址的文本文件
3. 设置合适的时间间隔
4. 点击"开始"按钮开始自动打开网址

URL文件格式：
- 每行一个网址
- 支持http://和https://协议
- 可以包含注释行（以#开头）
- 空行会被自动忽略

快捷键：
- Ctrl+O: 打开文件
- F5: 开始
- F6: 暂停
- F7/Esc: 停止

技术支持：
如有问题，请查看程序目录下的日志文件：auto_webpage_opener.log

版权信息：
© 2025 Auto Webpage Opener Team
"""
    
    readme_path = Path(__file__).parent / "dist" / "README.txt"
    readme_path.parent.mkdir(exist_ok=True)
    
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print(f"说明文件已创建: {readme_path}")

def create_sample_url_file():
    """创建示例URL文件"""
    sample_content = """# 示例URL文件
# 这是一个示例文件，展示如何创建URL列表

# 搜索引擎
http://www.baidu.com
https://www.google.com

# 新闻网站
https://www.sina.com.cn
https://www.163.com

# 技术网站
https://www.github.com
https://stackoverflow.com

# 注意：
# 1. 每行一个网址
# 2. 支持http://和https://
# 3. 以#开头的行是注释
# 4. 空行会被忽略
"""
    
    sample_path = Path(__file__).parent / "dist" / "示例URL文件.txt"
    sample_path.parent.mkdir(exist_ok=True)
    
    with open(sample_path, 'w', encoding='utf-8') as f:
        f.write(sample_content)
    
    print(f"示例URL文件已创建: {sample_path}")

def main():
    """主函数"""
    print("=" * 50)
    print("自动网页打开器 - 打包工具")
    print("=" * 50)
    
    # 构建可执行文件
    if build_executable():
        print("\n创建附加文件...")
        create_installer_script()
        create_readme()
        create_sample_url_file()
        
        print("\n" + "=" * 50)
        print("打包完成！")
        print("=" * 50)
        print("文件位置:")
        print("- 可执行文件: package/dist/自动网页打开器.exe")
        print("- 安装脚本: package/dist/install.bat")
        print("- 说明文件: package/dist/README.txt")
        print("- 示例文件: package/dist/示例URL文件.txt")
        print("\n您可以将dist目录中的所有文件打包分发。")
    else:
        print("\n打包失败！")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
