"""
创建应用程序图标
使用PIL库创建一个简单的图标
"""

try:
    from PIL import Image, ImageDraw, ImageFont
    import os
    
    def create_app_icon():
        """创建地球样式的应用程序图标"""
        # 创建256x256的图像
        size = 256
        img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)

        # 地球的中心和半径
        center = size // 2
        radius = center - 20

        # 绘制地球背景（蓝色）
        draw.ellipse([center-radius, center-radius, center+radius, center+radius],
                    fill=(30, 144, 255, 255), outline=(0, 100, 200, 255), width=3)

        # 绘制大陆（绿色）
        # 大陆1 - 类似非洲
        continent1_points = [
            (center-30, center-60), (center-10, center-80), (center+20, center-70),
            (center+30, center-40), (center+25, center-10), (center+15, center+20),
            (center-5, center+40), (center-20, center+30), (center-35, center+10),
            (center-40, center-20), (center-35, center-40)
        ]
        draw.polygon(continent1_points, fill=(34, 139, 34, 255))

        # 大陆2 - 类似欧洲
        continent2_points = [
            (center-50, center-70), (center-30, center-75), (center-20, center-65),
            (center-25, center-55), (center-45, center-60)
        ]
        draw.polygon(continent2_points, fill=(34, 139, 34, 255))

        # 大陆3 - 类似亚洲
        continent3_points = [
            (center+40, center-50), (center+60, center-40), (center+70, center-20),
            (center+65, center+10), (center+50, center+20), (center+45, center-10)
        ]
        draw.polygon(continent3_points, fill=(34, 139, 34, 255))

        # 绘制经纬线
        # 经线
        for i in range(-2, 3):
            x_offset = i * radius // 3
            # 绘制椭圆形经线
            draw.arc([center-radius+x_offset-10, center-radius,
                     center+radius+x_offset+10, center+radius],
                    0, 360, fill=(255, 255, 255, 100), width=1)

        # 纬线
        for i in range(-2, 3):
            y_offset = i * radius // 4
            y_pos = center + y_offset
            # 计算纬线宽度（球面效果）
            if abs(y_offset) < radius:
                line_width = int((radius**2 - y_offset**2)**0.5)
                draw.arc([center-line_width, y_pos-2, center+line_width, y_pos+2],
                        0, 360, fill=(255, 255, 255, 100), width=1)

        # 添加光泽效果
        # 高光
        highlight_radius = radius // 3
        highlight_x = center - radius // 3
        highlight_y = center - radius // 3
        draw.ellipse([highlight_x-highlight_radius//2, highlight_y-highlight_radius//2,
                     highlight_x+highlight_radius//2, highlight_y+highlight_radius//2],
                    fill=(255, 255, 255, 80))

        # 在底部添加小的"WWW"文字
        try:
            font_size = 24
            font = ImageFont.truetype("arial.ttf", font_size)
        except:
            font = ImageFont.load_default()

        text = "WWW"
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_x = (size - text_width) // 2
        text_y = center + radius + 10

        # 绘制文字阴影
        draw.text((text_x+1, text_y+1), text, fill=(0, 0, 0, 100), font=font)
        # 绘制文字
        draw.text((text_x, text_y), text, fill=(255, 255, 255, 255), font=font)
        
        # 保存为不同格式
        icon_dir = os.path.dirname(__file__)
        
        # 保存为PNG格式（用于窗口图标）
        png_path = os.path.join(icon_dir, "app_icon.png")
        img.save(png_path, "PNG")
        print(f"PNG图标已保存: {png_path}")
        
        # 创建ICO格式（用于Windows exe）
        ico_path = os.path.join(icon_dir, "app_icon.ico")
        # 创建多个尺寸的图标
        sizes = [16, 32, 48, 64, 128, 256]
        icons = []
        
        for icon_size in sizes:
            resized = img.resize((icon_size, icon_size), Image.Resampling.LANCZOS)
            icons.append(resized)
        
        # 保存为ICO文件
        icons[0].save(ico_path, format='ICO', sizes=[(icon.width, icon.height) for icon in icons])
        print(f"ICO图标已保存: {ico_path}")
        
        return png_path, ico_path
    
    if __name__ == "__main__":
        print("开始创建应用程序图标...")
        try:
            png_path, ico_path = create_app_icon()
            print("图标创建完成！")
            print(f"PNG图标: {png_path}")
            print(f"ICO图标: {ico_path}")
        except Exception as e:
            print(f"创建图标时发生错误: {e}")
            print("请确保已安装PIL库: pip install Pillow")

except ImportError:
    print("错误: 未找到PIL库")
    print("请安装PIL库: pip install Pillow")
    print("或者手动创建图标文件:")
    print("- app_icon.png (用于窗口图标)")
    print("- app_icon.ico (用于exe程序图标)")
