自动网页打开器 v1.0.0

这是一个用于按时间间隔自动打开网址列表的桌面应用程序。

功能特点：
- 支持从文本文件加载URL列表
- 可自定义时间间隔（0.5秒到1小时）
- 支持开始、暂停、停止控制
- 🆕 定时运行功能（每日自动执行）
- 🆕 系统托盘支持（后台运行）
- 实时进度显示
- 错误处理和日志记录
- 现代化的图形界面（地球🌍图标）

使用方法：
1. 运行"自动网页打开器.exe"
2. 点击"选择URL文件"按钮，选择包含网址的文本文件
3. 设置合适的时间间隔
4. 点击"开始"按钮开始自动打开网址

定时运行功能：
1. 先选择URL文件
2. 勾选"定时运行"复选框
3. 设置每日运行时间（如：06:00）
4. 保持程序运行，到达设定时间会自动开始
5. 每天只会自动执行一次

系统托盘功能：
1. 点击窗口右上角关闭按钮 → 隐藏到系统托盘（不退出程序）
2. 双击托盘图标 → 显示/隐藏窗口
3. 右键托盘图标 → 显示功能菜单（开始/暂停/停止/退出）
4. 托盘提示显示程序状态和定时信息
5. 适合设置定时运行后让程序在后台运行

URL文件格式：
- 每行一个网址
- 支持http://和https://协议
- 可以包含注释行（以#开头）
- 空行会被自动忽略

快捷键：
- Ctrl+O: 打开文件
- F5: 开始
- F6: 暂停
- F7/Esc: 停止

技术支持：
如有问题，请查看程序目录下的日志文件：auto_webpage_opener.log

版权信息：
© 2025 Auto Webpage Opener Team
