# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['E:\\开发项目\\待开发项目\\自动打开网页\\main.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=['PyQt5.QtCore', 'PyQt5.QtGui', 'PyQt5.QtWidgets', 'auto_webpage_opener.gui.main_window', 'auto_webpage_opener.services.url_manager', 'auto_webpage_opener.services.browser_opener', 'auto_webpage_opener.controllers.app_controller', 'auto_webpage_opener.controllers.url_worker'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='自动网页打开器',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=['E:\\开发项目\\待开发项目\\自动打开网页\\package\\app_icon.ico'],
)
