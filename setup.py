#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动网页打开器安装脚本
"""

from setuptools import setup, find_packages
import os

# 读取README文件
def read_readme():
    readme_path = os.path.join(os.path.dirname(__file__), 'README.md')
    if os.path.exists(readme_path):
        with open(readme_path, 'r', encoding='utf-8') as f:
            return f.read()
    return "自动网页打开器 - 一个用于按时间间隔自动打开网址列表的桌面应用程序"

# 读取requirements.txt
def read_requirements():
    requirements_path = os.path.join(os.path.dirname(__file__), 'requirements.txt')
    if os.path.exists(requirements_path):
        with open(requirements_path, 'r', encoding='utf-8') as f:
            return [line.strip() for line in f if line.strip() and not line.startswith('#')]
    return ['PyQt5>=5.15.0']

setup(
    name="auto-webpage-opener",
    version="1.0.0",
    author="Auto Webpage Opener Team",
    author_email="",
    description="一个用于按时间间隔自动打开网址列表的桌面应用程序",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 5 - Production/Stable",
        "Intended Audience :: End Users/Desktop",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Internet :: WWW/HTTP :: Browsers",
        "Topic :: Utilities",
    ],
    python_requires=">=3.8",
    install_requires=read_requirements(),
    entry_points={
        "console_scripts": [
            "auto-webpage-opener=auto_webpage_opener.app:main",
        ],
        "gui_scripts": [
            "auto-webpage-opener-gui=auto_webpage_opener.app:main",
        ],
    },
    include_package_data=True,
    package_data={
        "auto_webpage_opener": [
            "*.txt",
            "*.md",
        ],
    },
    keywords="webpage opener automation browser url",
    project_urls={
        "Bug Reports": "",
        "Source": "",
    },
)
